<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - Rules Editor</title>
    <meta property="csp-nonce" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <script type="module" crossorigin src="./assets/rules-CM7Mupdh.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BRONhjN7.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-Bsn_YzZH.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-DYitSqXV.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-YH5RXu5p.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/chat-context-BNzKQhCT.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/index-B528snJk.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-qUvJDeai.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-emviP3SE.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-CfWKsX8l.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-Bq1AWULp.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-B7q3rq9x.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/index-DL51nDXl.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/index-BS1SrqoN.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/remote-agents-client-oncIrPGY.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-CV6PzMi8.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-DLatYyzj.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/OpenFileButton-DaEVNMZt.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/lodash-E-8fRYvv.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/rules-model-CwG-l8cG.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-BmFLDXsf.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/RulesModeSelector-DgNhWqvf.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-left-CQtMd1xt.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-Bk4JGPpB.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B8PGiArX.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-KGoGPe8_.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/OpenFileButton-Bx7Exmp0.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/index-DeNt0W4u.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/rules-model-B6vv3aGc.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/RulesModeSelector-Qv_62MPy.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-C__BzIz3.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-CWo9IUaY.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-BzgVnrJx.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/rules-CeuJlYHL.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
