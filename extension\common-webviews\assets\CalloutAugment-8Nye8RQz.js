import{S,i as A,s as R,W as T,$ as x,a as v,d,G as U,t as $,q as u,a0 as y,g as V,a1 as p,c as f,K as W,N as g,O as k,a2 as N,E as B,j as D,R as w,o as F,p as H,T as E,U as G,V as K,Q as I,h as O}from"./SpinnerAugment-BRONhjN7.js";const J=o=>({}),b=o=>({});function j(o){let a,c;const i=o[8].icon,l=w(i,o,o[9],b);return{c(){a=g("div"),l&&l.c(),O(a,"class","c-callout-icon svelte-1u5qnh6")},m(e,n){f(e,a,n),l&&l.m(a,null),c=!0},p(e,n){l&&l.p&&(!c||512&n)&&E(l,i,e,e[9],c?K(i,e[9],n,J):G(e[9]),b)},i(e){c||(u(l,e),c=!0)},o(e){$(l,e),c=!1},d(e){e&&d(a),l&&l.d(e)}}}function L(o){let a,c,i,l=o[7].icon&&j(o);const e=o[8].default,n=w(e,o,o[9],null);return{c(){l&&l.c(),a=I(),c=g("div"),n&&n.c(),O(c,"class","c-callout-body svelte-1u5qnh6")},m(t,s){l&&l.m(t,s),f(t,a,s),f(t,c,s),n&&n.m(c,null),i=!0},p(t,s){t[7].icon?l?(l.p(t,s),128&s&&u(l,1)):(l=j(t),l.c(),u(l,1),l.m(a.parentNode,a)):l&&(F(),$(l,1,1,()=>{l=null}),H()),n&&n.p&&(!i||512&s)&&E(n,e,t,t[9],i?K(e,t[9],s,null):G(t[9]),null)},i(t){i||(u(l),u(n,t),i=!0)},o(t){$(l),$(n,t),i=!1},d(t){t&&(d(a),d(c)),l&&l.d(t),n&&n.d(t)}}}function M(o){let a,c,i,l;c=new T({props:{size:o[6],$$slots:{default:[L]},$$scope:{ctx:o}}});let e=[x(o[0]),{class:i=`c-callout c-callout--${o[0]} c-callout--${o[1]} c-callout--size-${o[2]} ${o[5]}`},o[4]],n={};for(let t=0;t<e.length;t+=1)n=v(n,e[t]);return{c(){a=g("div"),k(c.$$.fragment),y(a,n),p(a,"c-callout--highContrast",o[3]),p(a,"svelte-1u5qnh6",!0)},m(t,s){f(t,a,s),W(c,a,null),l=!0},p(t,[s]){const h={};640&s&&(h.$$scope={dirty:s,ctx:t}),c.$set(h),y(a,n=V(e,[1&s&&x(t[0]),(!l||39&s&&i!==(i=`c-callout c-callout--${t[0]} c-callout--${t[1]} c-callout--size-${t[2]} ${t[5]}`))&&{class:i},16&s&&t[4]])),p(a,"c-callout--highContrast",t[3]),p(a,"svelte-1u5qnh6",!0)},i(t){l||(u(c.$$.fragment,t),l=!0)},o(t){$(c.$$.fragment,t),l=!1},d(t){t&&d(a),U(c)}}}function P(o,a,c){let i,l;const e=["color","variant","size","highContrast"];let n=N(a,e),{$$slots:t={},$$scope:s}=a;const h=B(t);let{color:z="info"}=a,{variant:C="soft"}=a,{size:m=2}=a,{highContrast:q=!1}=a;const Q=m;return o.$$set=r=>{a=v(v({},a),D(r)),c(10,n=N(a,e)),"color"in r&&c(0,z=r.color),"variant"in r&&c(1,C=r.variant),"size"in r&&c(2,m=r.size),"highContrast"in r&&c(3,q=r.highContrast),"$$scope"in r&&c(9,s=r.$$scope)},o.$$.update=()=>{c(5,{class:i,...l}=n,i,(c(4,l),c(10,n)))},[z,C,m,q,l,i,Q,h,t,s]}class Y extends S{constructor(a){super(),A(this,a,P,M,R,{color:0,variant:1,size:2,highContrast:3})}}export{Y as C};
