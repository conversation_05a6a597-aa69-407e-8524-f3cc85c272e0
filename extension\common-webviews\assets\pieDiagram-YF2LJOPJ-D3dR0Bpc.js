import{p as P}from"./chunk-TMUBEWPD-rMpgRg7V.js";import{T as y,O,aF as E,_ as m,g as L,s as N,a as V,b as G,o as I,p as _,l as z,c as q,E as H,I as J,a4 as K,e as Q,x as U,G as X}from"./AugmentMessage-D7idHus4.js";import{p as Y}from"./gitGraph-YCYPL57B-takUKMan.js";import{d as B}from"./arc-BMgDSBV7.js";import{o as Z}from"./ordinal-_rw2EY4v.js";import"./SpinnerAugment-BRONhjN7.js";import"./IconButtonAugment-DYitSqXV.js";import"./CalloutAugment-8Nye8RQz.js";import"./CardAugment-Bq1AWULp.js";import"./index-DL51nDXl.js";import"./async-messaging-qUvJDeai.js";import"./message-broker-emviP3SE.js";import"./types-CGlLNakm.js";import"./file-paths-CfWKsX8l.js";import"./BaseTextInput-B7q3rq9x.js";import"./folder-opened-UvJxBo0O.js";import"./index-BS1SrqoN.js";import"./diff-operations-CLDm-KfY.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./SimpleMonaco-zl8B1j3Y.js";import"./index-BcKMBZ3o.js";import"./keypress-DD1aQVr0.js";import"./await_block-eLW-gO_q.js";import"./OpenFileButton-DaEVNMZt.js";import"./chat-context-BNzKQhCT.js";import"./index-B528snJk.js";import"./remote-agents-client-oncIrPGY.js";import"./ra-diff-ops-model-CV6PzMi8.js";import"./TextAreaAugment-DLatYyzj.js";import"./ButtonAugment-YH5RXu5p.js";import"./CollapseButtonAugment-mSbxnQHz.js";import"./partner-mcp-utils-DEO975Yb.js";import"./MaterialIcon-DRaAkbsC.js";import"./CopyButton-BImWfLKc.js";import"./ellipsis-BgT8k3VD.js";import"./IconFilePath-BmqHsAaQ.js";import"./LanguageIcon-D8OInxb5.js";import"./next-edit-types-904A5ehg.js";import"./Filespan-Fjc1Eq8J.js";import"./index-QgOjpL-u.js";import"./augment-logo-BlyUob3E.js";import"./pen-to-square-BZYwtsmR.js";import"./chevron-down-BmFLDXsf.js";import"./check-BJ4Ab2Wq.js";import"./_baseUniq-CsBAh6cQ.js";import"./_basePickBy-BlaPVPN5.js";import"./clone-CE1T4R8j.js";import"./init-g68aIKmP.js";function tt(t,a){return a<t?-1:a>t?1:a>=t?0:NaN}function et(t){return t}var at=X.pie,R={sections:new Map,showData:!1},M=R.sections,F=R.showData,rt=structuredClone(at),W={getConfig:m(()=>structuredClone(rt),"getConfig"),clear:m(()=>{M=new Map,F=R.showData,U()},"clear"),setDiagramTitle:_,getDiagramTitle:I,setAccTitle:G,getAccTitle:V,setAccDescription:N,getAccDescription:L,addSection:m(({label:t,value:a})=>{M.has(t)||(M.set(t,a),z.debug(`added new section: ${t}, with value: ${a}`))},"addSection"),getSections:m(()=>M,"getSections"),setShowData:m(t=>{F=t},"setShowData"),getShowData:m(()=>F,"getShowData")},it=m((t,a)=>{P(t,a),a.setShowData(t.showData),t.sections.map(a.addSection)},"populateDb"),nt={parse:m(async t=>{const a=await Y("pie",t);z.debug(a),it(a,W)},"parse")},ot=m(t=>`
  .pieCircle{
    stroke: ${t.pieStrokeColor};
    stroke-width : ${t.pieStrokeWidth};
    opacity : ${t.pieOpacity};
  }
  .pieOuterCircle{
    stroke: ${t.pieOuterStrokeColor};
    stroke-width: ${t.pieOuterStrokeWidth};
    fill: none;
  }
  .pieTitleText {
    text-anchor: middle;
    font-size: ${t.pieTitleTextSize};
    fill: ${t.pieTitleTextColor};
    font-family: ${t.fontFamily};
  }
  .slice {
    font-family: ${t.fontFamily};
    fill: ${t.pieSectionTextColor};
    font-size:${t.pieSectionTextSize};
    // fill: white;
  }
  .legend text {
    fill: ${t.pieLegendTextColor};
    font-family: ${t.fontFamily};
    font-size: ${t.pieLegendTextSize};
  }
`,"getStyles"),st=m(t=>{const a=[...t.entries()].map(s=>({label:s[0],value:s[1]})).sort((s,u)=>u.value-s.value);return function(){var s=et,u=tt,c=null,w=y(0),S=y(O),$=y(0);function r(e){var i,p,n,T,g,l=(e=E(e)).length,v=0,A=new Array(l),d=new Array(l),f=+w.apply(this,arguments),C=Math.min(O,Math.max(-O,S.apply(this,arguments)-f)),h=Math.min(Math.abs(C)/l,$.apply(this,arguments)),b=h*(C<0?-1:1);for(i=0;i<l;++i)(g=d[A[i]=i]=+s(e[i],i,e))>0&&(v+=g);for(u!=null?A.sort(function(x,D){return u(d[x],d[D])}):c!=null&&A.sort(function(x,D){return c(e[x],e[D])}),i=0,n=v?(C-l*b)/v:0;i<l;++i,f=T)p=A[i],T=f+((g=d[p])>0?g*n:0)+b,d[p]={data:e[p],index:i,value:g,startAngle:f,endAngle:T,padAngle:h};return d}return r.value=function(e){return arguments.length?(s=typeof e=="function"?e:y(+e),r):s},r.sortValues=function(e){return arguments.length?(u=e,c=null,r):u},r.sort=function(e){return arguments.length?(c=e,u=null,r):c},r.startAngle=function(e){return arguments.length?(w=typeof e=="function"?e:y(+e),r):w},r.endAngle=function(e){return arguments.length?(S=typeof e=="function"?e:y(+e),r):S},r.padAngle=function(e){return arguments.length?($=typeof e=="function"?e:y(+e),r):$},r}().value(s=>s.value)(a)},"createPieArcs"),re={parser:nt,db:W,renderer:{draw:m((t,a,s,u)=>{z.debug(`rendering pie chart
`+t);const c=u.db,w=q(),S=H(c.getConfig(),w.pie),$=18,r=450,e=r,i=J(a),p=i.append("g");p.attr("transform","translate(225,225)");const{themeVariables:n}=w;let[T]=K(n.pieOuterStrokeWidth);T??(T=2);const g=S.textPosition,l=Math.min(e,r)/2-40,v=B().innerRadius(0).outerRadius(l),A=B().innerRadius(l*g).outerRadius(l*g);p.append("circle").attr("cx",0).attr("cy",0).attr("r",l+T/2).attr("class","pieOuterCircle");const d=c.getSections(),f=st(d),C=[n.pie1,n.pie2,n.pie3,n.pie4,n.pie5,n.pie6,n.pie7,n.pie8,n.pie9,n.pie10,n.pie11,n.pie12],h=Z(C);p.selectAll("mySlices").data(f).enter().append("path").attr("d",v).attr("fill",o=>h(o.data.label)).attr("class","pieCircle");let b=0;d.forEach(o=>{b+=o}),p.selectAll("mySlices").data(f).enter().append("text").text(o=>(o.data.value/b*100).toFixed(0)+"%").attr("transform",o=>"translate("+A.centroid(o)+")").style("text-anchor","middle").attr("class","slice"),p.append("text").text(c.getDiagramTitle()).attr("x",0).attr("y",-200).attr("class","pieTitleText");const x=p.selectAll(".legend").data(h.domain()).enter().append("g").attr("class","legend").attr("transform",(o,k)=>"translate(216,"+(22*k-22*h.domain().length/2)+")");x.append("rect").attr("width",$).attr("height",$).style("fill",h).style("stroke",h),x.data(f).append("text").attr("x",22).attr("y",14).text(o=>{const{label:k,value:j}=o.data;return c.getShowData()?`${k} [${j}]`:k});const D=512+Math.max(...x.selectAll("text").nodes().map(o=>(o==null?void 0:o.getBoundingClientRect().width)??0));i.attr("viewBox",`0 0 ${D} 450`),Q(i,r,D,S.useMaxWidth)},"draw")},styles:ot};export{re as diagram};
