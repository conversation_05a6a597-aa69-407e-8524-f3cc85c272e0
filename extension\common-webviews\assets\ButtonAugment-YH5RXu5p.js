import{S as V,i as W,s as D,a as k,G as A,t as m,q as d,g as F,ae as H,K as I,O as U,a2 as E,E as J,j as M,d as h,o as b,p as z,a8 as G,h as f,c as p,e as K,N as v,Q as N,P as g,R as x,T as C,U as w,V as y,W as X}from"./SpinnerAugment-BRONhjN7.js";import{B as Y}from"./IconButtonAugment-DYitSqXV.js";const Z=a=>({}),O=a=>({}),_=a=>({}),P=a=>({});function Q(a){let n,l;const c=a[10].iconLeft,s=x(c,a,a[20],P);return{c(){n=v("div"),s&&s.c(),f(n,"class","c-button--icon svelte-14satks")},m(i,o){p(i,n,o),s&&s.m(n,null),l=!0},p(i,o){s&&s.p&&(!l||1048576&o)&&C(s,c,i,i[20],l?y(c,i[20],o,_):w(i[20]),P)},i(i){l||(d(s,i),l=!0)},o(i){m(s,i),l=!1},d(i){i&&h(n),s&&s.d(i)}}}function S(a){let n,l,c;return l=new X({props:{size:a[0]===.5?1:a[0],weight:a[1]==="ghost"?"regular":"medium",$$slots:{default:[nn]},$$scope:{ctx:a}}}),{c(){n=v("div"),U(l.$$.fragment),f(n,"class","c-button--text svelte-14satks")},m(s,i){p(s,n,i),I(l,n,null),c=!0},p(s,i){const o={};1&i&&(o.size=s[0]===.5?1:s[0]),2&i&&(o.weight=s[1]==="ghost"?"regular":"medium"),1048576&i&&(o.$$scope={dirty:i,ctx:s}),l.$set(o)},i(s){c||(d(l.$$.fragment,s),c=!0)},o(s){m(l.$$.fragment,s),c=!1},d(s){s&&h(n),A(l)}}}function nn(a){let n;const l=a[10].default,c=x(l,a,a[20],null);return{c(){c&&c.c()},m(s,i){c&&c.m(s,i),n=!0},p(s,i){c&&c.p&&(!n||1048576&i)&&C(c,l,s,s[20],n?y(l,s[20],i,null):w(s[20]),null)},i(s){n||(d(c,s),n=!0)},o(s){m(c,s),n=!1},d(s){c&&c.d(s)}}}function T(a){let n,l;const c=a[10].iconRight,s=x(c,a,a[20],O);return{c(){n=v("div"),s&&s.c(),f(n,"class","c-button--icon svelte-14satks")},m(i,o){p(i,n,o),s&&s.m(n,null),l=!0},p(i,o){s&&s.p&&(!l||1048576&o)&&C(s,c,i,i[20],l?y(c,i[20],o,Z):w(i[20]),O)},i(i){l||(d(s,i),l=!0)},o(i){m(s,i),l=!1},d(i){i&&h(n),s&&s.d(i)}}}function sn(a){let n,l,c,s,i,o=a[9].iconLeft&&Q(a),u=a[9].default&&S(a),r=a[9].iconRight&&T(a);return{c(){n=v("div"),o&&o.c(),l=N(),u&&u.c(),c=N(),r&&r.c(),f(n,"class",s=G(`c-button--content c-button--size-${a[0]}`)+" svelte-14satks")},m(t,$){p(t,n,$),o&&o.m(n,null),K(n,l),u&&u.m(n,null),K(n,c),r&&r.m(n,null),i=!0},p(t,$){t[9].iconLeft?o?(o.p(t,$),512&$&&d(o,1)):(o=Q(t),o.c(),d(o,1),o.m(n,l)):o&&(b(),m(o,1,1,()=>{o=null}),z()),t[9].default?u?(u.p(t,$),512&$&&d(u,1)):(u=S(t),u.c(),d(u,1),u.m(n,c)):u&&(b(),m(u,1,1,()=>{u=null}),z()),t[9].iconRight?r?(r.p(t,$),512&$&&d(r,1)):(r=T(t),r.c(),d(r,1),r.m(n,null)):r&&(b(),m(r,1,1,()=>{r=null}),z()),(!i||1&$&&s!==(s=G(`c-button--content c-button--size-${t[0]}`)+" svelte-14satks"))&&f(n,"class",s)},i(t){i||(d(o),d(u),d(r),i=!0)},o(t){m(o),m(u),m(r),i=!1},d(t){t&&h(n),o&&o.d(),u&&u.d(),r&&r.d()}}}function an(a){let n,l;const c=[{size:a[0]},{variant:a[1]},{color:a[2]},{highContrast:a[3]},{disabled:a[4]},{loading:a[6]},{alignment:a[7]},{radius:a[5]},a[8]];let s={$$slots:{default:[sn]},$$scope:{ctx:a}};for(let i=0;i<c.length;i+=1)s=k(s,c[i]);return n=new Y({props:s}),n.$on("click",a[11]),n.$on("keyup",a[12]),n.$on("keydown",a[13]),n.$on("mousedown",a[14]),n.$on("mouseover",a[15]),n.$on("focus",a[16]),n.$on("mouseleave",a[17]),n.$on("blur",a[18]),n.$on("contextmenu",a[19]),{c(){U(n.$$.fragment)},m(i,o){I(n,i,o),l=!0},p(i,[o]){const u=511&o?F(c,[1&o&&{size:i[0]},2&o&&{variant:i[1]},4&o&&{color:i[2]},8&o&&{highContrast:i[3]},16&o&&{disabled:i[4]},64&o&&{loading:i[6]},128&o&&{alignment:i[7]},32&o&&{radius:i[5]},256&o&&H(i[8])]):{};1049091&o&&(u.$$scope={dirty:o,ctx:i}),n.$set(u)},i(i){l||(d(n.$$.fragment,i),l=!0)},o(i){m(n.$$.fragment,i),l=!1},d(i){A(n,i)}}}function on(a,n,l){const c=["size","variant","color","highContrast","disabled","radius","loading","alignment"];let s=E(n,c),{$$slots:i={},$$scope:o}=n;const u=J(i);let{size:r=2}=n,{variant:t="solid"}=n,{color:$="neutral"}=n,{highContrast:R=!1}=n,{disabled:L=!1}=n,{radius:B="medium"}=n,{loading:j=!1}=n,{alignment:q="center"}=n;return a.$$set=e=>{n=k(k({},n),M(e)),l(8,s=E(n,c)),"size"in e&&l(0,r=e.size),"variant"in e&&l(1,t=e.variant),"color"in e&&l(2,$=e.color),"highContrast"in e&&l(3,R=e.highContrast),"disabled"in e&&l(4,L=e.disabled),"radius"in e&&l(5,B=e.radius),"loading"in e&&l(6,j=e.loading),"alignment"in e&&l(7,q=e.alignment),"$$scope"in e&&l(20,o=e.$$scope)},[r,t,$,R,L,B,j,q,s,u,i,function(e){g.call(this,a,e)},function(e){g.call(this,a,e)},function(e){g.call(this,a,e)},function(e){g.call(this,a,e)},function(e){g.call(this,a,e)},function(e){g.call(this,a,e)},function(e){g.call(this,a,e)},function(e){g.call(this,a,e)},function(e){g.call(this,a,e)},o]}class cn extends V{constructor(n){super(),W(this,n,on,an,D,{size:0,variant:1,color:2,highContrast:3,disabled:4,radius:5,loading:6,alignment:7})}}export{cn as B};
