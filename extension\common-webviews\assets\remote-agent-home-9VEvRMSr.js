import{S as B,i as G,s as j,a as dt,n as Y,d as m,b as ft,g as Qt,u as Vt,v as Xt,w as Yt,x as Jt,f as Zt,H as te,j as pt,W,G as _,t as u,q as g,c as f,K as x,e as z,N as b,O as y,Q as I,h as k,af as Bt,X as K,Y as O,D as L,o as H,p as T,a1 as X,L as Gt,a3 as wt,a4 as vt,a5 as ht,y as ot,al as jt,an as ee,z as _t,ah as ne}from"./SpinnerAugment-BRONhjN7.js";import"./design-system-init-Bsn_YzZH.js";/* empty css                                */import{e as F,u as Q,o as V,I as $t,h as se}from"./IconButtonAugment-DYitSqXV.js";import{M as re}from"./message-broker-emviP3SE.js";import{S as oe,T as Kt,a as le,b as ae,c as gt,d as ie,v as ce,e as de}from"./StatusIndicator-Bjp1cUxG.js";import{R as U,s as $e,a as ct}from"./index-B528snJk.js";import{T as st,a as rt,C as ge}from"./CardAugment-Bq1AWULp.js";import{C as ue}from"./CalloutAugment-8Nye8RQz.js";import{E as me}from"./exclamation-triangle-DkJg5qZC.js";import{d as fe,s as pe,R as lt}from"./remote-agents-client-oncIrPGY.js";import{A as we}from"./augment-logo-BlyUob3E.js";import"./async-messaging-qUvJDeai.js";import"./types-CGlLNakm.js";function ve(a){let t,n,e=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},a[0]],o={};for(let r=0;r<e.length;r+=1)o=dt(o,e[r]);return{c(){t=Zt("svg"),n=new te(!0),this.h()},l(r){t=Xt(r,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var l=Yt(t);n=Jt(l,!0),l.forEach(m),this.h()},h(){n.a=null,ft(t,o)},m(r,l){Vt(r,t,l),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M38.8 5.1C28.4-3.1 13.3-1.2 5.1 9.2s-6.3 25.5 4.1 33.7l592 464c10.4 8.2 25.5 6.3 33.7-4.1s6.3-25.5-4.1-33.7L481.4 352c9.4-.4 18.1-4.9 23.9-12.3 6.1-7.8 8.2-17.9 5.8-27.5l-6.2-25c-10.3-41.3-35.4-75.7-68.7-98.3L428.9 96l-3.7-48H456c4.4 0 8.6-1.2 12.2-3.3 7-4.2 11.8-11.9 11.8-20.7 0-13.3-10.7-24-24-24H184c-13.3 0-24 10.7-24 24 0 8.8 4.8 16.5 11.8 20.7 3.6 2.1 7.7 3.3 12.2 3.3h30.8l-3.7 48-3.2 41.6zm214.5 168.1 9.3-121.5c.1-1.2.1-2.5.1-3.7h114.5c0 1.2 0 2.5.1 3.7l10.8 140.9c1.1 14.6 8.8 27.8 20.9 36 23.9 16.2 41.7 40.8 49.1 70.2l1.3 5.1H420l-76-59.6V216c0-13.3-10.7-24-24-24-10.4 0-19.2 6.6-22.6 15.8l-44.2-34.6zM344 367l-80-63h-83.5l1.3-5.1c4-16.1 11.2-30.7 20.7-43.3l-37.7-29.7c-13.7 17.8-23.9 38.6-29.6 61.4l-6.2 25c-2.4 9.6-.2 19.7 5.8 27.5s15.4 12.3 25.2 12.3h136v136c0 13.3 10.7 24 24 24s24-10.7 24-24v-121z"/>',t)},p(r,[l]){ft(t,o=Qt(e,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 640 512"},1&l&&r[0]]))},i:Y,o:Y,d(r){r&&m(t)}}}function he(a,t,n){return a.$$set=e=>{n(0,t=dt(dt({},t),pt(e)))},[t=pt(t)]}class _e extends B{constructor(t){super(),G(this,t,he,ve,j,{})}}function xe(a){let t,n=a[0]?"Running in the cloud":"Running locally";return{c(){t=O(n)},m(e,o){f(e,t,o)},p(e,o){1&o&&n!==(n=e[0]?"Running in the cloud":"Running locally")&&K(t,n)},d(e){e&&m(t)}}}function ye(a){let t;return{c(){t=O("Unknown time")},m(n,e){f(n,t,e)},p:Y,d(n){n&&m(t)}}}function Se(a){let t;return{c(){t=O(a[3])},m(n,e){f(n,t,e)},p(n,e){8&e&&K(t,n[3])},d(n){n&&m(t)}}}function ke(a){let t,n,e,o=a[1]===U.agentRunning?"Last updated":"Started";function r(i,s){return i[2]?Se:ye}let l=r(a),d=l(a);return{c(){t=O(o),n=I(),d.c(),e=L()},m(i,s){f(i,t,s),f(i,n,s),d.m(i,s),f(i,e,s)},p(i,s){2&s&&o!==(o=i[1]===U.agentRunning?"Last updated":"Started")&&K(t,o),l===(l=r(i))&&d?d.p(i,s):(d.d(1),d=l(i),d&&(d.c(),d.m(e.parentNode,e)))},d(i){i&&(m(t),m(n),m(e)),d.d(i)}}}function Ae(a){let t,n,e,o,r,l;return n=new W({props:{size:1,color:"secondary",class:"location-text",$$slots:{default:[xe]},$$scope:{ctx:a}}}),r=new W({props:{size:1,color:"secondary",class:"time-text",$$slots:{default:[ke]},$$scope:{ctx:a}}}),{c(){t=b("div"),y(n.$$.fragment),e=I(),o=b("div"),y(r.$$.fragment),k(o,"class","time-container"),k(t,"class","agent-card-footer svelte-1qwlkoj")},m(d,i){f(d,t,i),x(n,t,null),z(t,e),z(t,o),x(r,o,null),l=!0},p(d,[i]){const s={};33&i&&(s.$$scope={dirty:i,ctx:d}),n.$set(s);const c={};46&i&&(c.$$scope={dirty:i,ctx:d}),r.$set(c)},i(d){l||(g(n.$$.fragment,d),g(r.$$.fragment,d),l=!0)},o(d){u(n.$$.fragment,d),u(r.$$.fragment,d),l=!1},d(d){d&&m(t),_(n),_(r)}}}function be(a,t,n){let{isRemote:e=!1}=t,{status:o}=t,{timestamp:r}=t,l=fe(r);const d=pe(r,i=>{n(3,l=i)});return Bt(()=>{d()}),a.$$set=i=>{"isRemote"in i&&n(0,e=i.isRemote),"status"in i&&n(1,o=i.status),"timestamp"in i&&n(2,r=i.timestamp)},[e,o,r,l]}class Re extends B{constructor(t){super(),G(this,t,be,Ae,j,{isRemote:0,status:1,timestamp:2})}}function Pe(a){let t;return{c(){t=O(a[0])},m(n,e){f(n,t,e)},p(n,e){1&e&&K(t,n[0])},d(n){n&&m(t)}}}function Ie(a){let t,n,e;return n=new W({props:{size:1,color:"secondary",$$slots:{default:[Pe]},$$scope:{ctx:a}}}),{c(){t=b("div"),y(n.$$.fragment),k(t,"class","task-text-container svelte-1tatwxk")},m(o,r){f(o,t,r),x(n,t,null),e=!0},p(o,r){const l={};9&r&&(l.$$scope={dirty:r,ctx:o}),n.$set(l)},i(o){e||(g(n.$$.fragment,o),e=!0)},o(o){u(n.$$.fragment,o),e=!1},d(o){o&&m(t),_(n)}}}function xt(a){let t,n,e;return n=new W({props:{size:1,color:a[1]==="error"?"error":"neutral",$$slots:{default:[De]},$$scope:{ctx:a}}}),{c(){t=b("div"),y(n.$$.fragment),k(t,"class","task-status-indicator svelte-1tatwxk")},m(o,r){f(o,t,r),x(n,t,null),e=!0},p(o,r){const l={};2&r&&(l.color=o[1]==="error"?"error":"neutral"),10&r&&(l.$$scope={dirty:r,ctx:o}),n.$set(l)},i(o){e||(g(n.$$.fragment,o),e=!0)},o(o){u(n.$$.fragment,o),e=!1},d(o){o&&m(t),_(n)}}}function De(a){let t,n=a[1]==="error"?"!":a[1]==="warning"?"⚠":"";return{c(){t=O(n)},m(e,o){f(e,t,o)},p(e,o){2&o&&n!==(n=e[1]==="error"?"!":e[1]==="warning"?"⚠":"")&&K(t,n)},d(e){e&&m(t)}}}function He(a){let t,n,e,o,r,l,d;r=new st({props:{content:a[0],triggerOn:[rt.Hover],maxWidth:"400px",$$slots:{default:[Ie]},$$scope:{ctx:a}}});let i=(a[1]==="error"||a[1]==="warning")&&xt(a);return{c(){t=b("div"),n=b("div"),o=I(),y(r.$$.fragment),l=I(),i&&i.c(),k(n,"class",e="bullet-point "+a[2]+" svelte-1tatwxk"),k(t,"class","task-item svelte-1tatwxk")},m(s,c){f(s,t,c),z(t,n),z(t,o),x(r,t,null),z(t,l),i&&i.m(t,null),d=!0},p(s,[c]){(!d||4&c&&e!==(e="bullet-point "+s[2]+" svelte-1tatwxk"))&&k(n,"class",e);const $={};1&c&&($.content=s[0]),9&c&&($.$$scope={dirty:c,ctx:s}),r.$set($),s[1]==="error"||s[1]==="warning"?i?(i.p(s,c),2&c&&g(i,1)):(i=xt(s),i.c(),g(i,1),i.m(t,null)):i&&(H(),u(i,1,1,()=>{i=null}),T())},i(s){d||(g(r.$$.fragment,s),g(i),d=!0)},o(s){u(r.$$.fragment,s),u(i),d=!1},d(s){s&&m(t),_(r),i&&i.d()}}}function Te(a,t,n){let e,{text:o}=t,{status:r="info"}=t;return a.$$set=l=>{"text"in l&&n(0,o=l.text),"status"in l&&n(1,r=l.status)},a.$$.update=()=>{2&a.$$.dirty&&n(2,e=function(l){switch(l){case"success":return"task-success";case"warning":return"task-warning";case"error":return"task-error";default:return"task-info"}}(r))},[o,r,e]}class ze extends B{constructor(t){super(),G(this,t,Te,He,j,{text:0,status:1})}}function yt(a,t,n){const e=a.slice();return e[19]=t[n],e[21]=n,e}function St(a){let t,n,e;return n=new ue({props:{variant:"soft",color:"error",size:1,$$slots:{icon:[Fe],default:[Ee]},$$scope:{ctx:a}}}),{c(){t=b("div"),y(n.$$.fragment),k(t,"class","deletion-error svelte-1bxdvw4")},m(o,r){f(o,t,r),x(n,t,null),e=!0},p(o,r){const l={};4194305&r&&(l.$$scope={dirty:r,ctx:o}),n.$set(l)},i(o){e||(g(n.$$.fragment,o),e=!0)},o(o){u(n.$$.fragment,o),e=!1},d(o){o&&m(t),_(n)}}}function Ee(a){let t,n,e,o,r;return{c(){t=O(a[0]),n=I(),e=b("button"),e.textContent="×",k(e,"class","error-dismiss svelte-1bxdvw4"),k(e,"aria-label","Dismiss error")},m(l,d){f(l,t,d),f(l,n,d),f(l,e,d),o||(r=Gt(e,"click",a[12]),o=!0)},p(l,d){1&d&&K(t,l[0])},d(l){l&&(m(t),m(n),m(e)),o=!1,r()}}}function Fe(a){let t,n;return t=new me({props:{slot:"icon"}}),{c(){y(t.$$.fragment)},m(e,o){x(t,e,o),n=!0},p:Y,i(e){n||(g(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function qe(a){let t,n;return t=new W({props:{size:2,weight:"medium",class:"session-text",$$slots:{default:[Me]},$$scope:{ctx:a}}}),{c(){y(t.$$.fragment)},m(e,o){x(t,e,o),n=!0},p(e,o){const r={};4194306&o&&(r.$$scope={dirty:o,ctx:e}),t.$set(r)},i(e){n||(g(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function Oe(a){let t,n,e,o,r,l;return e=new Kt({}),r=new W({props:{size:2,weight:"medium",$$slots:{default:[Ne]},$$scope:{ctx:a}}}),{c(){t=b("div"),n=b("div"),y(e.$$.fragment),o=I(),y(r.$$.fragment),k(n,"class","setup-script-badge svelte-1bxdvw4"),k(t,"class","setup-script-title-container svelte-1bxdvw4")},m(d,i){f(d,t,i),z(t,n),x(e,n,null),z(t,o),x(r,t,null),l=!0},p(d,i){const s={};4194304&i&&(s.$$scope={dirty:i,ctx:d}),r.$set(s)},i(d){l||(g(e.$$.fragment,d),g(r.$$.fragment,d),l=!0)},o(d){u(e.$$.fragment,d),u(r.$$.fragment,d),l=!1},d(d){d&&m(t),_(e),_(r)}}}function Me(a){let t,n=a[1].session_summary+"";return{c(){t=O(n)},m(e,o){f(e,t,o)},p(e,o){2&o&&n!==(n=e[1].session_summary+"")&&K(t,n)},d(e){e&&m(t)}}}function Ne(a){let t;return{c(){t=b("span"),t.textContent="Generate a setup script",k(t,"class","setup-script-title svelte-1bxdvw4")},m(n,e){f(n,t,e)},p:Y,d(n){n&&m(t)}}}function kt(a){let t,n,e=[],o=new Map,r=F(a[11].slice(0,3));const l=d=>d[21];for(let d=0;d<r.length;d+=1){let i=yt(a,r,d),s=l(i);o.set(s,e[d]=At(s,i))}return{c(){t=b("div");for(let d=0;d<e.length;d+=1)e[d].c();k(t,"class","tasks-list svelte-1bxdvw4")},m(d,i){f(d,t,i);for(let s=0;s<e.length;s+=1)e[s]&&e[s].m(t,null);n=!0},p(d,i){2048&i&&(r=F(d[11].slice(0,3)),H(),e=Q(e,i,l,1,d,r,o,t,V,At,null,yt),T())},i(d){if(!n){for(let i=0;i<r.length;i+=1)g(e[i]);n=!0}},o(d){for(let i=0;i<e.length;i+=1)u(e[i]);n=!1},d(d){d&&m(t);for(let i=0;i<e.length;i+=1)e[i].d()}}}function At(a,t){let n,e,o;return e=new ze({props:{text:t[19],status:"success"}}),{key:a,first:null,c(){n=L(),y(e.$$.fragment),this.first=n},m(r,l){f(r,n,l),x(e,r,l),o=!0},p(r,l){t=r;const d={};2048&l&&(d.text=t[19]),e.$set(d)},i(r){o||(g(e.$$.fragment,r),o=!0)},o(r){u(e.$$.fragment,r),o=!1},d(r){r&&m(n),_(e,r)}}}function Ce(a){let t,n;return t=new ae({}),{c(){y(t.$$.fragment)},m(e,o){x(t,e,o),n=!0},i(e){n||(g(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function Le(a){let t,n;return t=new _e({}),{c(){y(t.$$.fragment)},m(e,o){x(t,e,o),n=!0},i(e){n||(g(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function Ue(a){let t,n,e,o;const r=[Le,Ce],l=[];function d(i,s){return i[3]?0:1}return t=d(a),n=l[t]=r[t](a),{c(){n.c(),e=L()},m(i,s){l[t].m(i,s),f(i,e,s),o=!0},p(i,s){let c=t;t=d(i),t!==c&&(H(),u(l[c],1,1,()=>{l[c]=null}),T(),n=l[t],n||(n=l[t]=r[t](i),n.c()),g(n,1),n.m(e.parentNode,e))},i(i){o||(g(n),o=!0)},o(i){u(n),o=!1},d(i){i&&m(e),l[t].d(i)}}}function We(a){let t,n;return t=new $t({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[Ue]},$$scope:{ctx:a}}}),t.$on("click",a[13]),{c(){y(t.$$.fragment)},m(e,o){x(t,e,o),n=!0},p(e,o){const r={};4194312&o&&(r.$$scope={dirty:o,ctx:e}),t.$set(r)},i(e){n||(g(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function Be(a){let t,n;return t=new Kt({}),{c(){y(t.$$.fragment)},m(e,o){x(t,e,o),n=!0},i(e){n||(g(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function Ge(a){let t,n;return t=new $t({props:{disabled:!a[9],variant:"ghost",color:"neutral",size:1,title:a[9]?"SSH to agent":"SSH to agent (agent must be running or idle)",$$slots:{default:[Be]},$$scope:{ctx:a}}}),t.$on("click",a[14]),{c(){y(t.$$.fragment)},m(e,o){x(t,e,o),n=!0},p(e,o){const r={};512&o&&(r.disabled=!e[9]),512&o&&(r.title=e[9]?"SSH to agent":"SSH to agent (agent must be running or idle)"),4194304&o&&(r.$$scope={dirty:o,ctx:e}),t.$set(r)},i(e){n||(g(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function je(a){let t,n;return t=new le({}),{c(){y(t.$$.fragment)},m(e,o){x(t,e,o),n=!0},i(e){n||(g(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function Ke(a){let t,n;return t=new $t({props:{variant:"ghost",color:"neutral",size:1,disabled:a[6],title:a[6]?"Deleting agent...":"Delete agent",$$slots:{default:[je]},$$scope:{ctx:a}}}),t.$on("click",a[15]),{c(){y(t.$$.fragment)},m(e,o){x(t,e,o),n=!0},p(e,o){const r={};64&o&&(r.disabled=e[6]),64&o&&(r.title=e[6]?"Deleting agent...":"Delete agent"),4194304&o&&(r.$$scope={dirty:o,ctx:e}),t.$set(r)},i(e){n||(g(t.$$.fragment,e),n=!0)},o(e){u(t.$$.fragment,e),n=!1},d(e){_(t,e)}}}function Qe(a){let t,n,e,o,r,l,d,i,s,c,$,v,p,w,S,A,N,R,q,M;const tt=[Oe,qe],D=[];function C(h,P){return h[1].is_setup_script_agent?0:1}e=C(a),o=D[e]=tt[e](a),i=new oe({props:{status:a[1].status,workspaceStatus:a[1].workspace_status,isExpanded:!0,hasUpdates:a[1].has_updates}});let E=a[11].length>0&&kt(a);return p=new st({props:{content:a[3]?"Unpin agent":"Pin agent",triggerOn:[rt.Hover],side:"top",$$slots:{default:[We]},$$scope:{ctx:a}}}),S=new st({props:{content:"SSH to agent",triggerOn:[rt.Hover],side:"top",$$slots:{default:[Ge]},$$scope:{ctx:a}}}),N=new st({props:{content:"Delete agent",triggerOn:[rt.Hover],side:"top",$$slots:{default:[Ke]},$$scope:{ctx:a}}}),q=new Re({props:{isRemote:a[10],status:a[1].status,timestamp:a[1].updated_at||a[1].started_at}}),{c(){t=b("div"),n=b("div"),o.c(),l=I(),d=b("div"),y(i.$$.fragment),s=I(),c=b("div"),E&&E.c(),$=I(),v=b("div"),y(p.$$.fragment),w=I(),y(S.$$.fragment),A=I(),y(N.$$.fragment),R=I(),y(q.$$.fragment),k(n,"class","session-summary-container svelte-1bxdvw4"),k(n,"title",r=a[1].is_setup_script_agent?"Generate a setup script":a[1].session_summary),k(d,"class","card-info"),k(t,"class","card-header svelte-1bxdvw4"),k(c,"class","card-content svelte-1bxdvw4"),k(v,"class","card-actions svelte-1bxdvw4")},m(h,P){f(h,t,P),z(t,n),D[e].m(n,null),z(t,l),z(t,d),x(i,d,null),f(h,s,P),f(h,c,P),E&&E.m(c,null),f(h,$,P),f(h,v,P),x(p,v,null),z(v,w),x(S,v,null),z(v,A),x(N,v,null),f(h,R,P),x(q,h,P),M=!0},p(h,P){let at=e;e=C(h),e===at?D[e].p(h,P):(H(),u(D[at],1,1,()=>{D[at]=null}),T(),o=D[e],o?o.p(h,P):(o=D[e]=tt[e](h),o.c()),g(o,1),o.m(n,null)),(!M||2&P&&r!==(r=h[1].is_setup_script_agent?"Generate a setup script":h[1].session_summary))&&k(n,"title",r);const et={};2&P&&(et.status=h[1].status),2&P&&(et.workspaceStatus=h[1].workspace_status),2&P&&(et.hasUpdates=h[1].has_updates),i.$set(et),h[11].length>0?E?(E.p(h,P),2048&P&&g(E,1)):(E=kt(h),E.c(),g(E,1),E.m(c,null)):E&&(H(),u(E,1,1,()=>{E=null}),T());const it={};8&P&&(it.content=h[3]?"Unpin agent":"Pin agent"),4194440&P&&(it.$$scope={dirty:P,ctx:h}),p.$set(it);const ut={};4195072&P&&(ut.$$scope={dirty:P,ctx:h}),S.$set(ut);const mt={};4194400&P&&(mt.$$scope={dirty:P,ctx:h}),N.$set(mt);const nt={};1024&P&&(nt.isRemote=h[10]),2&P&&(nt.status=h[1].status),2&P&&(nt.timestamp=h[1].updated_at||h[1].started_at),q.$set(nt)},i(h){M||(g(o),g(i.$$.fragment,h),g(E),g(p.$$.fragment,h),g(S.$$.fragment,h),g(N.$$.fragment,h),g(q.$$.fragment,h),M=!0)},o(h){u(o),u(i.$$.fragment,h),u(E),u(p.$$.fragment,h),u(S.$$.fragment,h),u(N.$$.fragment,h),u(q.$$.fragment,h),M=!1},d(h){h&&(m(t),m(s),m(c),m($),m(v),m(R)),D[e].d(),_(i),E&&E.d(),_(p),_(S),_(N),_(q,h)}}}function Ve(a){let t,n,e,o,r=a[0]&&St(a);return e=new ge({props:{variant:"surface",size:2,interactive:!0,class:"agent-card",$$slots:{default:[Qe]},$$scope:{ctx:a}}}),e.$on("click",a[16]),e.$on("keydown",a[17]),{c(){t=b("div"),r&&r.c(),n=I(),y(e.$$.fragment),k(t,"class","card-wrapper svelte-1bxdvw4"),X(t,"selected-card",a[2]),X(t,"setup-script-card",a[1].is_setup_script_agent),X(t,"deleting",a[6])},m(l,d){f(l,t,d),r&&r.m(t,null),z(t,n),x(e,t,null),o=!0},p(l,[d]){l[0]?r?(r.p(l,d),1&d&&g(r,1)):(r=St(l),r.c(),g(r,1),r.m(t,n)):r&&(H(),u(r,1,1,()=>{r=null}),T());const i={};4198378&d&&(i.$$scope={dirty:d,ctx:l}),e.$set(i),(!o||4&d)&&X(t,"selected-card",l[2]),(!o||2&d)&&X(t,"setup-script-card",l[1].is_setup_script_agent),(!o||64&d)&&X(t,"deleting",l[6])},i(l){o||(g(r),g(e.$$.fragment,l),o=!0)},o(l){u(r),u(e.$$.fragment,l),o=!1},d(l){l&&m(t),r&&r.d(),_(e)}}}function Xe(a,t,n){let e,o,{agent:r}=t,{selected:l=!1}=t,{isPinned:d=!1}=t,{onSelect:i}=t,{onDelete:s}=t,{deletionError:c=null}=t,{isDeleting:$=!1}=t,{onTogglePinned:v}=t,{onSSH:p}=t,{canSsh:w=!1}=t;function S(){n(0,c=null)}return Bt(()=>{S()}),a.$$set=A=>{"agent"in A&&n(1,r=A.agent),"selected"in A&&n(2,l=A.selected),"isPinned"in A&&n(3,d=A.isPinned),"onSelect"in A&&n(4,i=A.onSelect),"onDelete"in A&&n(5,s=A.onDelete),"deletionError"in A&&n(0,c=A.deletionError),"isDeleting"in A&&n(6,$=A.isDeleting),"onTogglePinned"in A&&n(7,v=A.onTogglePinned),"onSSH"in A&&n(8,p=A.onSSH),"canSsh"in A&&n(9,w=A.canSsh)},a.$$.update=()=>{2&a.$$.dirty&&n(11,e=r.turn_summaries||[])},n(10,o=!0),[c,r,l,d,i,s,$,v,p,w,!0,e,S,A=>{A.stopPropagation(),v()},A=>{p&&(A.stopPropagation(),p())},A=>{A.stopPropagation(),s()},()=>i(r.remote_agent_id),A=>A.key==="Enter"&&i(r.remote_agent_id)]}class Ye extends B{constructor(t){super(),G(this,t,Xe,Ve,j,{agent:1,selected:2,isPinned:3,onSelect:4,onDelete:5,deletionError:0,isDeleting:6,onTogglePinned:7,onSSH:8,canSsh:9})}}function Je(a){let t,n,e,o;function r(i){a[15](i)}function l(i){a[16](i)}let d={agent:a[0],selected:a[1],isPinned:a[6],onSelect:a[2],onDelete:a[13],onTogglePinned:a[14],onSSH:a[10],canSsh:a[5]};return a[4]!==void 0&&(d.deletionError=a[4]),a[3]!==void 0&&(d.isDeleting=a[3]),t=new Ye({props:d}),wt.push(()=>vt(t,"deletionError",r)),wt.push(()=>vt(t,"isDeleting",l)),{c(){y(t.$$.fragment)},m(i,s){x(t,i,s),o=!0},p(i,[s]){const c={};1&s&&(c.agent=i[0]),2&s&&(c.selected=i[1]),64&s&&(c.isPinned=i[6]),4&s&&(c.onSelect=i[2]),1&s&&(c.onDelete=i[13]),1&s&&(c.onTogglePinned=i[14]),32&s&&(c.canSsh=i[5]),!n&&16&s&&(n=!0,c.deletionError=i[4],ht(()=>n=!1)),!e&&8&s&&(e=!0,c.isDeleting=i[3],ht(()=>e=!1)),t.$set(c)},i(i){o||(g(t.$$.fragment,i),o=!0)},o(i){u(t.$$.fragment,i),o=!1},d(i){_(t,i)}}}function Ze(a,t,n){let e,o,r,l,{agent:d}=t,{selected:i=!1}=t,{onSelect:s}=t;const c=ot(lt.key),$=ot(gt);jt(a,$,R=>n(12,l=R));let v=!1,p=null,w=null;async function S(R){var M,tt;A(),n(3,v=!0);const q=((M=l.state)==null?void 0:M.agentOverviews)||[];try{if(!await c.deleteRemoteAgent(R))throw new Error("Failed to delete agent");if($.update(D=>{if(D)return{...D,agentOverviews:D.agentOverviews.filter(C=>C.remote_agent_id!==R)}}),(((tt=l.state)==null?void 0:tt.pinnedAgents)||{})[R])try{await c.deletePinnedAgentFromStore(R);const D=await c.getPinnedAgentsFromStore();$.update(C=>{if(C)return{...C,pinnedAgents:D}})}catch(D){console.error("Failed to remove pinned status:",D)}}catch(D){console.error("Failed to delete agent:",D),$.update(C=>{if(C)return{...C,agentOverviews:q}}),n(4,p=D instanceof Error?D.message:"Failed to delete agent"),w=setTimeout(()=>{A()},5e3)}finally{n(3,v=!1)}}function A(){n(4,p=null),w&&(clearTimeout(w),w=null)}async function N(R){try{o?await c.deletePinnedAgentFromStore(R):await c.savePinnedAgentToStore(R,!0);const q=await c.getPinnedAgentsFromStore();$.update(M=>{if(M)return{...M,pinnedAgents:q}})}catch(q){console.error("Failed to toggle pinned status:",q)}}return a.$$set=R=>{"agent"in R&&n(0,d=R.agent),"selected"in R&&n(1,i=R.selected),"onSelect"in R&&n(2,s=R.onSelect)},a.$$.update=()=>{var R;4096&a.$$.dirty&&n(11,e=((R=l.state)==null?void 0:R.pinnedAgents)||{}),2049&a.$$.dirty&&n(6,o=(e==null?void 0:e[d.remote_agent_id])===!0),1&a.$$.dirty&&n(5,r=d.status===U.agentRunning||d.status===U.agentIdle)},[d,i,s,v,p,r,o,$,S,N,async function(){return!!r&&await(async R=>await c.sshToRemoteAgent(R.remote_agent_id))(d)},e,l,()=>S(d.remote_agent_id),()=>N(d.remote_agent_id),function(R){p=R,n(4,p)},function(R){v=R,n(3,v)}]}class J extends B{constructor(t){super(),G(this,t,Ze,Je,j,{agent:0,selected:1,onSelect:2})}}function tn(a){let t;return{c(){t=O(a[0])},m(n,e){f(n,t,e)},p(n,e){1&e&&K(t,n[0])},d(n){n&&m(t)}}}function en(a){let t,n,e;return n=new W({props:{size:2,color:"secondary",$$slots:{default:[tn]},$$scope:{ctx:a}}}),{c(){t=b("div"),y(n.$$.fragment),k(t,"class","section-header svelte-1tegnqi")},m(o,r){f(o,t,r),x(n,t,null),e=!0},p(o,[r]){const l={};3&r&&(l.$$scope={dirty:r,ctx:o}),n.$set(l)},i(o){e||(g(n.$$.fragment,o),e=!0)},o(o){u(n.$$.fragment,o),e=!1},d(o){o&&m(t),_(n)}}}function nn(a,t,n){let{title:e}=t;return a.$$set=o=>{"title"in o&&n(0,e=o.title)},[e]}class Z extends B{constructor(t){super(),G(this,t,nn,en,j,{title:0})}}function bt(a,t,n){const e=a.slice();return e[7]=t[n],e[9]=n,e}function Rt(a,t,n){const e=a.slice();return e[7]=t[n],e[9]=n,e}function Pt(a,t,n){const e=a.slice();return e[7]=t[n],e[9]=n,e}function It(a,t,n){const e=a.slice();return e[7]=t[n],e[9]=n,e}function Dt(a,t,n){const e=a.slice();return e[7]=t[n],e[9]=n,e}function Ht(a,t,n){const e=a.slice();return e[7]=t[n],e[9]=n,e}function sn(a){let t,n,e,o,r,l,d,i=a[1].pinned.length>0&&Tt(a),s=a[1].readyToReview.length>0&&Et(a),c=a[1].running.length>0&&qt(a),$=a[1].idle.length>0&&Mt(a),v=a[1].failed.length>0&&Ct(a),p=a[1].additional.length>0&&Ut(a);return{c(){i&&i.c(),t=I(),s&&s.c(),n=I(),c&&c.c(),e=I(),$&&$.c(),o=I(),v&&v.c(),r=I(),p&&p.c(),l=L()},m(w,S){i&&i.m(w,S),f(w,t,S),s&&s.m(w,S),f(w,n,S),c&&c.m(w,S),f(w,e,S),$&&$.m(w,S),f(w,o,S),v&&v.m(w,S),f(w,r,S),p&&p.m(w,S),f(w,l,S),d=!0},p(w,S){w[1].pinned.length>0?i?(i.p(w,S),2&S&&g(i,1)):(i=Tt(w),i.c(),g(i,1),i.m(t.parentNode,t)):i&&(H(),u(i,1,1,()=>{i=null}),T()),w[1].readyToReview.length>0?s?(s.p(w,S),2&S&&g(s,1)):(s=Et(w),s.c(),g(s,1),s.m(n.parentNode,n)):s&&(H(),u(s,1,1,()=>{s=null}),T()),w[1].running.length>0?c?(c.p(w,S),2&S&&g(c,1)):(c=qt(w),c.c(),g(c,1),c.m(e.parentNode,e)):c&&(H(),u(c,1,1,()=>{c=null}),T()),w[1].idle.length>0?$?($.p(w,S),2&S&&g($,1)):($=Mt(w),$.c(),g($,1),$.m(o.parentNode,o)):$&&(H(),u($,1,1,()=>{$=null}),T()),w[1].failed.length>0?v?(v.p(w,S),2&S&&g(v,1)):(v=Ct(w),v.c(),g(v,1),v.m(r.parentNode,r)):v&&(H(),u(v,1,1,()=>{v=null}),T()),w[1].additional.length>0?p?(p.p(w,S),2&S&&g(p,1)):(p=Ut(w),p.c(),g(p,1),p.m(l.parentNode,l)):p&&(H(),u(p,1,1,()=>{p=null}),T())},i(w){d||(g(i),g(s),g(c),g($),g(v),g(p),d=!0)},o(w){u(i),u(s),u(c),u($),u(v),u(p),d=!1},d(w){w&&(m(t),m(n),m(e),m(o),m(r),m(l)),i&&i.d(w),s&&s.d(w),c&&c.d(w),$&&$.d(w),v&&v.d(w),p&&p.d(w)}}}function rn(a){let t,n,e;return n=new W({props:{size:3,color:"secondary",$$slots:{default:[ln]},$$scope:{ctx:a}}}),{c(){t=b("div"),y(n.$$.fragment),k(t,"class","empty-state svelte-aiqmvp")},m(o,r){f(o,t,r),x(n,t,null),e=!0},p(o,r){const l={};32768&r&&(l.$$scope={dirty:r,ctx:o}),n.$set(l)},i(o){e||(g(n.$$.fragment,o),e=!0)},o(o){u(n.$$.fragment,o),e=!1},d(o){o&&m(t),_(n)}}}function on(a){let t,n,e,o,r,l;return e=new ee({}),r=new W({props:{size:3,color:"secondary",$$slots:{default:[an]},$$scope:{ctx:a}}}),{c(){t=b("div"),n=b("div"),y(e.$$.fragment),o=I(),y(r.$$.fragment),k(n,"class","l-loading-container svelte-aiqmvp"),k(t,"class","empty-state svelte-aiqmvp")},m(d,i){f(d,t,i),z(t,n),x(e,n,null),z(n,o),x(r,n,null),l=!0},p(d,i){const s={};32768&i&&(s.$$scope={dirty:i,ctx:d}),r.$set(s)},i(d){l||(g(e.$$.fragment,d),g(r.$$.fragment,d),l=!0)},o(d){u(e.$$.fragment,d),u(r.$$.fragment,d),l=!1},d(d){d&&m(t),_(e),_(r)}}}function Tt(a){let t,n,e,o,r=[],l=new Map;t=new Z({props:{title:"Pinned"}});let d=F(a[1].pinned);const i=s=>s[7].remote_agent_id+s[9];for(let s=0;s<d.length;s+=1){let c=Ht(a,d,s),$=i(c);l.set($,r[s]=zt($,c))}return{c(){y(t.$$.fragment),n=I(),e=b("div");for(let s=0;s<r.length;s+=1)r[s].c();k(e,"class","agent-grid svelte-aiqmvp")},m(s,c){x(t,s,c),f(s,n,c),f(s,e,c);for(let $=0;$<r.length;$+=1)r[$]&&r[$].m(e,null);o=!0},p(s,c){11&c&&(d=F(s[1].pinned),H(),r=Q(r,c,i,1,s,d,l,e,V,zt,null,Ht),T())},i(s){if(!o){g(t.$$.fragment,s);for(let c=0;c<d.length;c+=1)g(r[c]);o=!0}},o(s){u(t.$$.fragment,s);for(let c=0;c<r.length;c+=1)u(r[c]);o=!1},d(s){s&&(m(n),m(e)),_(t,s);for(let c=0;c<r.length;c+=1)r[c].d()}}}function zt(a,t){var r;let n,e,o;return e=new J({props:{agent:t[7],selected:t[7].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:a,first:null,c(){n=L(),y(e.$$.fragment),this.first=n},m(l,d){f(l,n,d),x(e,l,d),o=!0},p(l,d){var s;t=l;const i={};2&d&&(i.agent=t[7]),3&d&&(i.selected=t[7].remote_agent_id===((s=t[0].state)==null?void 0:s.selectedAgentId)),e.$set(i)},i(l){o||(g(e.$$.fragment,l),o=!0)},o(l){u(e.$$.fragment,l),o=!1},d(l){l&&m(n),_(e,l)}}}function Et(a){let t,n,e,o,r=[],l=new Map;t=new Z({props:{title:"Ready to review"}});let d=F(a[1].readyToReview);const i=s=>s[7].remote_agent_id+s[9];for(let s=0;s<d.length;s+=1){let c=Dt(a,d,s),$=i(c);l.set($,r[s]=Ft($,c))}return{c(){y(t.$$.fragment),n=I(),e=b("div");for(let s=0;s<r.length;s+=1)r[s].c();k(e,"class","agent-grid svelte-aiqmvp")},m(s,c){x(t,s,c),f(s,n,c),f(s,e,c);for(let $=0;$<r.length;$+=1)r[$]&&r[$].m(e,null);o=!0},p(s,c){11&c&&(d=F(s[1].readyToReview),H(),r=Q(r,c,i,1,s,d,l,e,V,Ft,null,Dt),T())},i(s){if(!o){g(t.$$.fragment,s);for(let c=0;c<d.length;c+=1)g(r[c]);o=!0}},o(s){u(t.$$.fragment,s);for(let c=0;c<r.length;c+=1)u(r[c]);o=!1},d(s){s&&(m(n),m(e)),_(t,s);for(let c=0;c<r.length;c+=1)r[c].d()}}}function Ft(a,t){var r;let n,e,o;return e=new J({props:{agent:t[7],selected:t[7].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:a,first:null,c(){n=L(),y(e.$$.fragment),this.first=n},m(l,d){f(l,n,d),x(e,l,d),o=!0},p(l,d){var s;t=l;const i={};2&d&&(i.agent=t[7]),3&d&&(i.selected=t[7].remote_agent_id===((s=t[0].state)==null?void 0:s.selectedAgentId)),e.$set(i)},i(l){o||(g(e.$$.fragment,l),o=!0)},o(l){u(e.$$.fragment,l),o=!1},d(l){l&&m(n),_(e,l)}}}function qt(a){let t,n,e,o,r=[],l=new Map;t=new Z({props:{title:"Running agents"}});let d=F(a[1].running);const i=s=>s[7].remote_agent_id+s[9];for(let s=0;s<d.length;s+=1){let c=It(a,d,s),$=i(c);l.set($,r[s]=Ot($,c))}return{c(){y(t.$$.fragment),n=I(),e=b("div");for(let s=0;s<r.length;s+=1)r[s].c();k(e,"class","agent-grid svelte-aiqmvp")},m(s,c){x(t,s,c),f(s,n,c),f(s,e,c);for(let $=0;$<r.length;$+=1)r[$]&&r[$].m(e,null);o=!0},p(s,c){11&c&&(d=F(s[1].running),H(),r=Q(r,c,i,1,s,d,l,e,V,Ot,null,It),T())},i(s){if(!o){g(t.$$.fragment,s);for(let c=0;c<d.length;c+=1)g(r[c]);o=!0}},o(s){u(t.$$.fragment,s);for(let c=0;c<r.length;c+=1)u(r[c]);o=!1},d(s){s&&(m(n),m(e)),_(t,s);for(let c=0;c<r.length;c+=1)r[c].d()}}}function Ot(a,t){var r;let n,e,o;return e=new J({props:{agent:t[7],selected:t[7].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:a,first:null,c(){n=L(),y(e.$$.fragment),this.first=n},m(l,d){f(l,n,d),x(e,l,d),o=!0},p(l,d){var s;t=l;const i={};2&d&&(i.agent=t[7]),3&d&&(i.selected=t[7].remote_agent_id===((s=t[0].state)==null?void 0:s.selectedAgentId)),e.$set(i)},i(l){o||(g(e.$$.fragment,l),o=!0)},o(l){u(e.$$.fragment,l),o=!1},d(l){l&&m(n),_(e,l)}}}function Mt(a){let t,n,e,o,r=[],l=new Map;t=new Z({props:{title:"Idle agents"}});let d=F(a[1].idle);const i=s=>s[7].remote_agent_id+s[9];for(let s=0;s<d.length;s+=1){let c=Pt(a,d,s),$=i(c);l.set($,r[s]=Nt($,c))}return{c(){y(t.$$.fragment),n=I(),e=b("div");for(let s=0;s<r.length;s+=1)r[s].c();k(e,"class","agent-grid svelte-aiqmvp")},m(s,c){x(t,s,c),f(s,n,c),f(s,e,c);for(let $=0;$<r.length;$+=1)r[$]&&r[$].m(e,null);o=!0},p(s,c){11&c&&(d=F(s[1].idle),H(),r=Q(r,c,i,1,s,d,l,e,V,Nt,null,Pt),T())},i(s){if(!o){g(t.$$.fragment,s);for(let c=0;c<d.length;c+=1)g(r[c]);o=!0}},o(s){u(t.$$.fragment,s);for(let c=0;c<r.length;c+=1)u(r[c]);o=!1},d(s){s&&(m(n),m(e)),_(t,s);for(let c=0;c<r.length;c+=1)r[c].d()}}}function Nt(a,t){var r;let n,e,o;return e=new J({props:{agent:t[7],selected:t[7].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:a,first:null,c(){n=L(),y(e.$$.fragment),this.first=n},m(l,d){f(l,n,d),x(e,l,d),o=!0},p(l,d){var s;t=l;const i={};2&d&&(i.agent=t[7]),3&d&&(i.selected=t[7].remote_agent_id===((s=t[0].state)==null?void 0:s.selectedAgentId)),e.$set(i)},i(l){o||(g(e.$$.fragment,l),o=!0)},o(l){u(e.$$.fragment,l),o=!1},d(l){l&&m(n),_(e,l)}}}function Ct(a){let t,n,e,o,r=[],l=new Map;t=new Z({props:{title:"Failed agents"}});let d=F(a[1].failed);const i=s=>s[7].remote_agent_id+s[9];for(let s=0;s<d.length;s+=1){let c=Rt(a,d,s),$=i(c);l.set($,r[s]=Lt($,c))}return{c(){y(t.$$.fragment),n=I(),e=b("div");for(let s=0;s<r.length;s+=1)r[s].c();k(e,"class","agent-grid svelte-aiqmvp")},m(s,c){x(t,s,c),f(s,n,c),f(s,e,c);for(let $=0;$<r.length;$+=1)r[$]&&r[$].m(e,null);o=!0},p(s,c){11&c&&(d=F(s[1].failed),H(),r=Q(r,c,i,1,s,d,l,e,V,Lt,null,Rt),T())},i(s){if(!o){g(t.$$.fragment,s);for(let c=0;c<d.length;c+=1)g(r[c]);o=!0}},o(s){u(t.$$.fragment,s);for(let c=0;c<r.length;c+=1)u(r[c]);o=!1},d(s){s&&(m(n),m(e)),_(t,s);for(let c=0;c<r.length;c+=1)r[c].d()}}}function Lt(a,t){var r;let n,e,o;return e=new J({props:{agent:t[7],selected:t[7].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:a,first:null,c(){n=L(),y(e.$$.fragment),this.first=n},m(l,d){f(l,n,d),x(e,l,d),o=!0},p(l,d){var s;t=l;const i={};2&d&&(i.agent=t[7]),3&d&&(i.selected=t[7].remote_agent_id===((s=t[0].state)==null?void 0:s.selectedAgentId)),e.$set(i)},i(l){o||(g(e.$$.fragment,l),o=!0)},o(l){u(e.$$.fragment,l),o=!1},d(l){l&&m(n),_(e,l)}}}function Ut(a){let t,n,e,o,r=[],l=new Map;t=new Z({props:{title:"Other agents"}});let d=F(a[1].additional);const i=s=>s[7].remote_agent_id+s[9];for(let s=0;s<d.length;s+=1){let c=bt(a,d,s),$=i(c);l.set($,r[s]=Wt($,c))}return{c(){y(t.$$.fragment),n=I(),e=b("div");for(let s=0;s<r.length;s+=1)r[s].c();k(e,"class","agent-grid svelte-aiqmvp")},m(s,c){x(t,s,c),f(s,n,c),f(s,e,c);for(let $=0;$<r.length;$+=1)r[$]&&r[$].m(e,null);o=!0},p(s,c){11&c&&(d=F(s[1].additional),H(),r=Q(r,c,i,1,s,d,l,e,V,Wt,null,bt),T())},i(s){if(!o){g(t.$$.fragment,s);for(let c=0;c<d.length;c+=1)g(r[c]);o=!0}},o(s){u(t.$$.fragment,s);for(let c=0;c<r.length;c+=1)u(r[c]);o=!1},d(s){s&&(m(n),m(e)),_(t,s);for(let c=0;c<r.length;c+=1)r[c].d()}}}function Wt(a,t){var r;let n,e,o;return e=new J({props:{agent:t[7],selected:t[7].remote_agent_id===((r=t[0].state)==null?void 0:r.selectedAgentId),onSelect:t[3]}}),{key:a,first:null,c(){n=L(),y(e.$$.fragment),this.first=n},m(l,d){f(l,n,d),x(e,l,d),o=!0},p(l,d){var s;t=l;const i={};2&d&&(i.agent=t[7]),3&d&&(i.selected=t[7].remote_agent_id===((s=t[0].state)==null?void 0:s.selectedAgentId)),e.$set(i)},i(l){o||(g(e.$$.fragment,l),o=!0)},o(l){u(e.$$.fragment,l),o=!1},d(l){l&&m(n),_(e,l)}}}function ln(a){let t;return{c(){t=O("No agents available")},m(n,e){f(n,t,e)},d(n){n&&m(t)}}}function an(a){let t;return{c(){t=O("Loading the Augment panel...")},m(n,e){f(n,t,e)},d(n){n&&m(t)}}}function cn(a){let t,n,e,o;const r=[on,rn,sn],l=[];function d(i,s){var c,$;return(c=i[0].state)!=null&&c.agentOverviews?(($=i[0].state)==null?void 0:$.agentOverviews.length)===0?1:2:0}return n=d(a),e=l[n]=r[n](a),{c(){t=b("div"),e.c(),k(t,"class","agent-list svelte-aiqmvp")},m(i,s){f(i,t,s),l[n].m(t,null),o=!0},p(i,[s]){let c=n;n=d(i),n===c?l[n].p(i,s):(H(),u(l[c],1,1,()=>{l[c]=null}),T(),e=l[n],e?e.p(i,s):(e=l[n]=r[n](i),e.c()),g(e,1),e.m(t,null))},i(i){o||(g(e),o=!0)},o(i){u(e),o=!1},d(i){i&&m(t),l[n].d()}}}function dn(a,t,n){let e,o,r,l;const d=ot(gt);jt(a,d,s=>n(0,l=s));const i=ot(lt.key);return a.$$.update=()=>{var s,c,$;1&a.$$.dirty&&n(5,e=$e(((s=l.state)==null?void 0:s.agentOverviews)||[])),1&a.$$.dirty&&n(4,o=((c=l.state)==null?void 0:c.pinnedAgents)||{}),48&a.$$.dirty&&n(1,r=e.reduce((v,p)=>((o==null?void 0:o[p.remote_agent_id])===!0?v.pinned.push(p):p.status===U.agentIdle&&p.has_updates?v.readyToReview.push(p):p.status===U.agentRunning||p.status===U.agentStarting||p.workspace_status===ct.workspaceResuming?v.running.push(p):p.status===U.agentFailed?v.failed.push(p):p.status===U.agentIdle||p.workspace_status===ct.workspacePaused||p.workspace_status===ct.workspacePausing?v.idle.push(p):v.additional.push(p),v),{pinned:[],readyToReview:[],running:[],idle:[],failed:[],additional:[]})),1&a.$$.dirty&&(($=l.state)!=null&&$.agentOverviews||i.focusAugmentPanel())},[l,r,d,function(s){d.update(c=>{if(c)return{...c,selectedAgentId:s}})},o,e]}class $n extends B{constructor(t){super(),G(this,t,dn,cn,j,{})}}function gn(a){let t,n,e,o,r,l,d,i,s,c;return o=new we({}),d=new $n({}),{c(){t=b("div"),n=b("h1"),e=b("span"),y(o.$$.fragment),r=O(`
    Remote Agents`),l=I(),y(d.$$.fragment),k(e,"class","l-main__title-logo svelte-1941nw6"),k(n,"class","l-main__title svelte-1941nw6"),k(t,"class","l-main svelte-1941nw6")},m($,v){f($,t,v),z(t,n),z(n,e),x(o,e,null),z(n,r),z(t,l),x(d,t,null),i=!0,s||(c=Gt(window,"message",a[0].onMessageFromExtension),s=!0)},p:Y,i($){i||(g(o.$$.fragment,$),g(d.$$.fragment,$),i=!0)},o($){u(o.$$.fragment,$),u(d.$$.fragment,$),i=!1},d($){$&&m(t),_(o),_(d),s=!1,c()}}}function un(a){const t=new re(se),n=new ie(t,void 0,ce,de);t.registerConsumer(n),_t(gt,n);const e=new lt(t);return _t(lt.key,e),ne(()=>(n.fetchStateFromExtension().then(()=>{n.update(o=>{if(!o)return;const r=[...o.activeWebviews,"home"];return o.pinnedAgents?{...o,activeWebviews:r}:{...o,activeWebviews:r,pinnedAgents:{}}})}),()=>{t.dispose(),e.dispose()})),[t]}new class extends B{constructor(a){super(),G(this,a,un,gn,j,{})}}({target:document.getElementById("app")});
