import{S as N,i as b,s as V,a as w,n as v,d as f,b as C,g as M,u as S,v as j,w as A,x as D,f as G,H as I,j as y,G as B,t as u,q as m,c as H,K as L,N as K,O as P,h as O,E as R,R as T,T as q,U as E,V as F,o as U,p as J,D as Q}from"./SpinnerAugment-BRONhjN7.js";import{S as W}from"./TextAreaAugment-DLatYyzj.js";function X(i){let o,e,s=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},i[0]],t={};for(let n=0;n<s.length;n+=1)t=w(t,s[n]);return{c(){o=G("svg"),e=new I(!0),this.h()},l(n){o=j(n,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var a=A(o);e=D(a,!0),a.forEach(f),this.h()},h(){e.a=null,C(o,t)},m(n,a){S(n,o,a),e.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M280 64h40c35.3 0 64 28.7 64 64v320c0 35.3-28.7 64-64 64H64c-35.3 0-64-28.7-64-64V128c0-35.3 28.7-64 64-64h49.6C121 27.5 153.3 0 192 0s71 27.5 78.4 64zM64 112c-8.8 0-16 7.2-16 16v320c0 8.8 7.2 16 16 16h256c8.8 0 16-7.2 16-16V128c0-8.8-7.2-16-16-16h-16v24c0 13.3-10.7 24-24 24H104c-13.3 0-24-10.7-24-24v-24zm128-8a24 24 0 1 0 0-48 24 24 0 1 0 0 48"/>',o)},p(n,[a]){C(o,t=M(s,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 384 512"},1&a&&n[0]]))},i:v,o:v,d(n){n&&f(o)}}}function Y(i,o,e){return i.$$set=s=>{e(0,o=w(w({},o),y(s)))},[o=y(o)]}class Z extends N{constructor(o){super(),b(this,o,Y,X,V,{})}}const _=i=>({}),k=i=>({}),oo=i=>({}),z=i=>({});function to(i){let o;const e=i[10].text,s=T(e,i,i[11],k);return{c(){s&&s.c()},m(t,n){s&&s.m(t,n),o=!0},p(t,n){s&&s.p&&(!o||2048&n)&&q(s,e,t,t[11],o?F(e,t[11],n,_):E(t[11]),k)},i(t){o||(m(s,t),o=!0)},o(t){u(s,t),o=!1},d(t){s&&s.d(t)}}}function eo(i){let o,e;return o=new Z({}),{c(){P(o.$$.fragment)},m(s,t){L(o,s,t),e=!0},p:v,i(s){e||(m(o.$$.fragment,s),e=!0)},o(s){u(o.$$.fragment,s),e=!1},d(s){B(o,s)}}}function so(i){let o;const e=i[10].icon,s=T(e,i,i[11],z);return{c(){s&&s.c()},m(t,n){s&&s.m(t,n),o=!0},p(t,n){s&&s.p&&(!o||2048&n)&&q(s,e,t,t[11],o?F(e,t[11],n,oo):E(t[11]),z)},i(t){o||(m(s,t),o=!0)},o(t){u(s,t),o=!1},d(t){s&&s.d(t)}}}function no(i){let o,e,s,t;const n=[so,eo],a=[];function d(r,l){return r[8].icon?0:1}return o=d(i),e=a[o]=n[o](i),{c(){e.c(),s=Q()},m(r,l){a[o].m(r,l),H(r,s,l),t=!0},p(r,l){let p=o;o=d(r),o===p?a[o].p(r,l):(U(),u(a[p],1,1,()=>{a[p]=null}),J(),e=a[o],e?e.p(r,l):(e=a[o]=n[o](r),e.c()),m(e,1),e.m(s.parentNode,s))},i(r){t||(m(e),t=!0)},o(r){u(e),t=!1},d(r){r&&f(s),a[o].d(r)}}}function io(i){let o,e,s;return e=new W({props:{defaultColor:i[2],size:i[0],variant:i[1],loading:i[7],stickyColor:i[4],tooltip:{neutral:i[3],success:"Copied!"},stateVariant:{success:"soft"},onClick:i[5],icon:!i[8].text,tooltipNested:i[6],$$slots:{iconLeft:[no],default:[to]},$$scope:{ctx:i}}}),{c(){o=K("span"),P(e.$$.fragment),O(o,"class","c-copy-button svelte-tq93gm")},m(t,n){H(t,o,n),L(e,o,null),s=!0},p(t,[n]){const a={};4&n&&(a.defaultColor=t[2]),1&n&&(a.size=t[0]),2&n&&(a.variant=t[1]),128&n&&(a.loading=t[7]),16&n&&(a.stickyColor=t[4]),8&n&&(a.tooltip={neutral:t[3],success:"Copied!"}),32&n&&(a.onClick=t[5]),256&n&&(a.icon=!t[8].text),64&n&&(a.tooltipNested=t[6]),2304&n&&(a.$$scope={dirty:n,ctx:t}),e.$set(a)},i(t){s||(m(e.$$.fragment,t),s=!0)},o(t){u(e.$$.fragment,t),s=!1},d(t){t&&f(o),B(e)}}}function ao(i,o){return new Promise(e=>setTimeout(e,i,o))}function co(i,o,e){let{$$slots:s={},$$scope:t}=o;const n=R(s);let{size:a=1}=o,{variant:d="ghost-block"}=o,{color:r="neutral"}=o,{text:l}=o,{tooltip:p="Copy"}=o,{stickyColor:x=!1}=o,{onCopy:g=async()=>{if(l!==void 0){e(7,$=!0);try{await Promise.all([navigator.clipboard.writeText(typeof l=="string"?l:await l()),ao(250)])}finally{e(7,$=!1)}return"success"}}}=o,{tooltipNested:h}=o,$=!1;return i.$$set=c=>{"size"in c&&e(0,a=c.size),"variant"in c&&e(1,d=c.variant),"color"in c&&e(2,r=c.color),"text"in c&&e(9,l=c.text),"tooltip"in c&&e(3,p=c.tooltip),"stickyColor"in c&&e(4,x=c.stickyColor),"onCopy"in c&&e(5,g=c.onCopy),"tooltipNested"in c&&e(6,h=c.tooltipNested),"$$scope"in c&&e(11,t=c.$$scope)},[a,d,r,p,x,g,h,$,n,l,s,t]}class po extends N{constructor(o){super(),b(this,o,co,io,V,{size:0,variant:1,color:2,text:9,tooltip:3,stickyColor:4,onCopy:5,tooltipNested:6})}}export{po as C,Z as a};
