var Fn=Object.defineProperty;var An=(s,t,e)=>t in s?Fn(s,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):s[t]=e;var Wt=(s,t,e)=>An(s,typeof t!="symbol"?t+"":t,e);import{S as vt,i as yt,s as ht,n as at,d as g,h as w,a1 as gt,c as h,e as E,N as z,a3 as Mt,X as ct,Y as R,G as L,t as m,q as u,K as M,O as D,I as rn,J as qt,ag as kn,L as jt,Q as S,ah as Zt,af as an,aa as cn,o as G,p as Y,F as zn,al as Lt,A as Gt,a7 as At,aw as fe,D as xt,z as ie,y as Rt,a4 as Ht,a5 as Bt,am as Ln,W as lt,ax as Tt,ai as pe,aj as Mn,f as dn,an as Xt}from"./SpinnerAugment-BRONhjN7.js";import"./design-system-init-Bsn_YzZH.js";/* empty css                                */import{W as ue,I as ae,e as dt,u as fn,o as pn,h as un,g as Dn}from"./IconButtonAugment-DYitSqXV.js";import{M as Nn}from"./message-broker-emviP3SE.js";import{R as Vt}from"./ra-diff-ops-model-CV6PzMi8.js";import{s as $e}from"./index-QgOjpL-u.js";import{c as se,p as qn,M as En,g as Ot,a as On,i as Pn,b as Rn,P as te,O as $n,D as Tn,C as Vn,E as Sn}from"./diff-operations-CLDm-KfY.js";import{C as jn,a as Hn,T as mn,b as Bn}from"./CollapseButtonAugment-mSbxnQHz.js";import{a as Un,g as Zn,M as In}from"./index-BcKMBZ3o.js";import{g as Wn}from"./SimpleMonaco-zl8B1j3Y.js";import{V as ce}from"./VSCodeCodicon-CGIXiTeX.js";import{d as Jn,T as Et,a as Yt,C as Gn}from"./CardAugment-Bq1AWULp.js";import{B as Ft}from"./ButtonAugment-YH5RXu5p.js";import{M as de}from"./MaterialIcon-DRaAkbsC.js";import{i as Yn,b as Ut,c as Kn,d as me,n as Qn,a as Kt,g as ft,M as Xn}from"./file-paths-CfWKsX8l.js";import{F as ti,g as Jt,p as ge,d as ei}from"./index-B528snJk.js";import{L as gn}from"./LanguageIcon-D8OInxb5.js";import{A as ni}from"./async-messaging-qUvJDeai.js";import{E as hn}from"./exclamation-triangle-DkJg5qZC.js";import{F as ii}from"./Filespan-Fjc1Eq8J.js";import{M as si}from"./ModalAugment-CGgB_q36.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./index-BS1SrqoN.js";class Qt{constructor(t){Wt(this,"_opts",null);Wt(this,"_subscribers",new Set);this._asyncMsgSender=t}subscribe(t){return this._subscribers.add(t),t(this),()=>{this._subscribers.delete(t)}}notifySubscribers(){this._subscribers.forEach(t=>t(this))}get opts(){return this._opts}updateOpts(t){this._opts=t,this.notifySubscribers()}async onPanelLoaded(){try{this.updateOpts(null);const t=await this._asyncMsgSender.send({type:ue.remoteAgentDiffPanelLoaded});this.updateOpts(t.data)}catch(t){console.error("Failed to load diff panel:",t),this.updateOpts(null)}}handleMessageFromExtension(t){const e=t.data;return!(!e||!e.type)&&e.type===ue.remoteAgentDiffPanelSetOpts&&(this.updateOpts(e.data),!0)}}Wt(Qt,"key","remoteAgentDiffModel");function oi(s){let t;return{c(){t=R(s[1])},m(e,n){h(e,t,n)},p(e,n){2&n&&ct(t,e[1])},d(e){e&&g(t)}}}function li(s){let t;return{c(){t=R(s[1])},m(e,n){h(e,t,n)},p(e,n){2&n&&ct(t,e[1])},d(e){e&&g(t)}}}function ri(s){let t,e,n;function i(a,r){return a[2]?li:oi}let o=i(s),l=o(s);return{c(){t=z("span"),e=z("code"),l.c(),w(e,"class","markdown-codespan svelte-11ta4gi"),w(e,"style",n=s[2]?`background-color: ${s[1]}; color: ${s[3]?"white":"black"}`:""),gt(e,"markdown-string",s[4])},m(a,r){h(a,t,r),E(t,e),l.m(e,null),s[6](t)},p(a,[r]){o===(o=i(a))&&l?l.p(a,r):(l.d(1),l=o(a),l&&(l.c(),l.m(e,null))),14&r&&n!==(n=a[2]?`background-color: ${a[1]}; color: ${a[3]?"white":"black"}`:"")&&w(e,"style",n),16&r&&gt(e,"markdown-string",a[4])},i:at,o:at,d(a){a&&g(t),l.d(),s[6](null)}}}function ai(s,t,e){let n,i,o,l,{token:a}=t,{element:r}=t;return s.$$set=c=>{"token"in c&&e(5,a=c.token),"element"in c&&e(0,r=c.element)},s.$$.update=()=>{32&s.$$.dirty&&e(1,n=a.raw.slice(1,a.raw.length-1)),2&s.$$.dirty&&e(4,i=n.startsWith('"')),2&s.$$.dirty&&e(2,o=/^#[0-9a-fA-F]{6}|#[0-9a-fA-F]{3}/.test(n)),6&s.$$.dirty&&e(3,l=o&&function(c){if(!/^#([0-9A-F]{3}|[0-9A-F]{6})$/i.test(c))throw new Error('Invalid hex color format. Expected "#RGB" or "#RRGGBB"');let d,f,p;return c.length===4?(d=parseInt(c.charAt(1),16),f=parseInt(c.charAt(2),16),p=parseInt(c.charAt(3),16),d*=17,f*=17,p*=17):(d=parseInt(c.slice(1,3),16),f=parseInt(c.slice(3,5),16),p=parseInt(c.slice(5,7),16)),.299*d+.587*f+.114*p<130}(n))},[r,n,o,l,i,a,function(c){Mt[c?"unshift":"push"](()=>{r=c,e(0,r)})}]}class ci extends vt{constructor(t){super(),yt(this,t,ai,ri,ht,{token:5,element:0})}}function di(s){let t,e;return t=new En({props:{markdown:s[1](s[0]),renderers:s[2]}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,[i]){const o={};1&i&&(o.markdown=n[1](n[0])),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function fi(s,t,e){let{markdown:n}=t;const i={codespan:ci};return s.$$set=o=>{"markdown"in o&&e(0,n=o.markdown)},[n,o=>o.replace(/`?#[0-9a-fA-F]{3,6}`?/g,l=>l.startsWith("`")?l:`\`${l}\``),i]}class oe extends vt{constructor(t){super(),yt(this,t,fi,di,ht,{markdown:0})}}function he(s,t){return`${s}:${t}`}const pi=(s,t)=>{let e=null,n=null,i=null,o=!1;function l(){i&&cancelAnimationFrame(i),i=requestAnimationFrame(()=>{const{path:d,onCollapseStateChange:f}=t;if(o)return void(i=null);const p=Array.from(document.querySelectorAll(`[data-description-id^="${d}:"]`));let k=!1;for(const C of p)if(C!==s&&a(s,C)){k=!0;break}k&&(o=!0),f&&f(k),i=null})}function a(d,f){const p=d.getBoundingClientRect(),k=f.getBoundingClientRect();return!(p.bottom<=k.top||k.bottom<=p.top)}function r(){c(),e=new MutationObserver(()=>{l()});const d=s.closest(".descriptions")||document.body;e.observe(d,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["style","data-description-id"]}),window.ResizeObserver&&(n=new ResizeObserver(()=>{l()}),n.observe(s)),window.addEventListener("resize",l),window.addEventListener("scroll",l)}function c(){e&&(e.disconnect(),e=null),n&&(n.disconnect(),n=null),i&&(cancelAnimationFrame(i),i=null),window.removeEventListener("resize",l),window.removeEventListener("scroll",l)}return document.readyState==="loading"?document.addEventListener("DOMContentLoaded",()=>{r(),l()}):requestAnimationFrame(()=>{r(),l()}),{update:d=>{t=d,o=!1,l()},destroy:c}};function ui(s){let t,e,n,i,o,l,a,r,c,d,f,p,k;return n=new oe({props:{markdown:s[0].text}}),l=new oe({props:{markdown:s[7]}}),{c(){t=z("div"),e=z("div"),D(n.$$.fragment),i=S(),o=z("div"),D(l.$$.fragment),a=S(),r=z("div"),r.textContent="hover to expand",w(e,"class","c-diff-description__content svelte-wweiw1"),w(r,"class","c-diff-description__expand-hint svelte-wweiw1"),w(o,"class","c-diff-description__truncated-content svelte-wweiw1"),w(t,"class","c-diff-description svelte-wweiw1"),qt(t,"top",s[1]+"px"),w(t,"data-description-id",c=he(s[2],s[3])),w(t,"role","region"),w(t,"aria-label","Code diff description"),gt(t,"c-diff-description__collapsed",s[6]&&!s[5]),gt(t,"c-diff-description__hovered",s[5])},m(C,v){h(C,t,v),E(t,e),M(n,e,null),E(t,i),E(t,o),M(l,o,null),E(o,a),E(o,r),s[10](t),f=!0,p||(k=[kn(d=pi.call(null,t,{path:s[2],onCollapseStateChange:s[11]})),jt(t,"mouseenter",s[12]),jt(t,"mouseleave",s[13])],p=!0)},p(C,[v]){const F={};1&v&&(F.markdown=C[0].text),n.$set(F);const N={};128&v&&(N.markdown=C[7]),l.$set(N),(!f||2&v)&&qt(t,"top",C[1]+"px"),(!f||12&v&&c!==(c=he(C[2],C[3])))&&w(t,"data-description-id",c),d&&cn(d.update)&&68&v&&d.update.call(null,{path:C[2],onCollapseStateChange:C[11]}),(!f||96&v)&&gt(t,"c-diff-description__collapsed",C[6]&&!C[5]),(!f||32&v)&&gt(t,"c-diff-description__hovered",C[5])},i(C){f||(u(n.$$.fragment,C),u(l.$$.fragment,C),f=!0)},o(C){m(n.$$.fragment,C),m(l.$$.fragment,C),f=!1},d(C){C&&g(t),L(n),L(l),s[10](null),p=!1,rn(k)}}}function $i(s){const t=document.createElement("canvas").getContext("2d");return t?t.measureText(s).width:8*s.length}function mi(s,t,e){let n,i,{description:o}=t,{position:l}=t,{fileId:a}=t,{index:r}=t,c=!1,d=!1,f=0;const p=Jn(v=>{e(5,c=v)},100);function k(){if(i){const v=i.getBoundingClientRect();e(9,f=v.width-128)}}let C=null;return Zt(()=>{i&&typeof ResizeObserver<"u"&&(C=new ResizeObserver(()=>{k()}),C.observe(i),k())}),an(()=>{C&&C.disconnect()}),s.$$set=v=>{"description"in v&&e(0,o=v.description),"position"in v&&e(1,l=v.position),"fileId"in v&&e(2,a=v.fileId),"index"in v&&e(3,r=v.index)},s.$$.update=()=>{16&s.$$.dirty&&i&&k(),513&s.$$.dirty&&e(7,n=(()=>{const v=o.text.split(`
`)[0].split(" ");let F="";if(f<=0)for(const N of v){const b=F+(F?" ":"")+N;if(b.length>30)break;F=b}else for(const N of v){const b=F+(F?" ":"")+N;if($i(b+"...")>f)break;F=b}return F+"..."})())},[o,l,a,r,i,c,d,n,p,f,function(v){Mt[v?"unshift":"push"](()=>{i=v,e(4,i)})},v=>e(6,d=v),v=>{p.cancel(),e(5,c=!0),v.stopPropagation()},v=>{p(!1),v.stopPropagation()}]}class gi extends vt{constructor(t){super(),yt(this,t,mi,ui,ht,{description:0,position:1,fileId:2,index:3})}}function ve(s,t,e){const n=s.slice();return n[48]=t[e],n[50]=e,n}function ye(s){let t,e,n,i,o;e=new ae({props:{variant:"ghost",color:"neutral",size:1,$$slots:{default:[yi]},$$scope:{ctx:s}}}),e.$on("click",s[25]);let l=dt(s[1]),a=[];for(let c=0;c<l.length;c+=1)a[c]=we(ve(s,l,c));const r=c=>m(a[c],1,1,()=>{a[c]=null});return{c(){t=z("div"),D(e.$$.fragment),n=S(),i=z("div");for(let c=0;c<a.length;c+=1)a[c].c();w(t,"class","toggle-button svelte-1r29xbx"),w(i,"class","descriptions svelte-1r29xbx"),qt(i,"transform","translateY("+-s[4]+"px)")},m(c,d){h(c,t,d),M(e,t,null),h(c,n,d),h(c,i,d);for(let f=0;f<a.length;f+=1)a[f]&&a[f].m(i,null);o=!0},p(c,d){const f={};if(1&d[0]|1048576&d[1]&&(f.$$scope={dirty:d,ctx:c}),e.$set(f),1570&d[0]){let p;for(l=dt(c[1]),p=0;p<l.length;p+=1){const k=ve(c,l,p);a[p]?(a[p].p(k,d),u(a[p],1)):(a[p]=we(k),a[p].c(),u(a[p],1),a[p].m(i,null))}for(G(),p=l.length;p<a.length;p+=1)r(p);Y()}(!o||16&d[0])&&qt(i,"transform","translateY("+-c[4]+"px)")},i(c){if(!o){u(e.$$.fragment,c);for(let d=0;d<l.length;d+=1)u(a[d]);o=!0}},o(c){m(e.$$.fragment,c),a=a.filter(Boolean);for(let d=0;d<a.length;d+=1)m(a[d]);o=!1},d(c){c&&(g(t),g(n),g(i)),L(e),At(a,c)}}}function hi(s){let t,e;return t=new ce({props:{icon:"book"}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function vi(s){let t,e;return t=new ce({props:{icon:"x"}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function yi(s){let t,e,n,i;const o=[vi,hi],l=[];function a(r,c){return r[0]?0:1}return t=a(s),e=l[t]=o[t](s),{c(){e.c(),n=xt()},m(r,c){l[t].m(r,c),h(r,n,c),i=!0},p(r,c){let d=t;t=a(r),t!==d&&(G(),m(l[d],1,1,()=>{l[d]=null}),Y(),e=l[t],e||(e=l[t]=o[t](r),e.c()),u(e,1),e.m(n.parentNode,n))},i(r){i||(u(e),i=!0)},o(r){m(e),i=!1},d(r){r&&g(n),l[t].d(r)}}}function we(s){let t,e;return t=new gi({props:{description:s[48],position:s[5][s[50]]||s[9](s[48]),fileId:s[10],index:s[50]}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};2&i[0]&&(o.description=n[48]),34&i[0]&&(o.position=n[5][n[50]]||n[9](n[48])),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function wi(s){let t,e,n,i,o=s[1].length>0&&ye(s);return{c(){t=z("div"),e=z("div"),n=S(),o&&o.c(),w(e,"class","editor-container svelte-1r29xbx"),qt(e,"height",s[3]+"px"),w(t,"class","monaco-diff-container svelte-1r29xbx"),gt(t,"monaco-diff-container-with-descriptions",s[1].length>0&&s[0])},m(l,a){h(l,t,a),E(t,e),s[24](e),E(t,n),o&&o.m(t,null),i=!0},p(l,a){(!i||8&a[0])&&qt(e,"height",l[3]+"px"),l[1].length>0?o?(o.p(l,a),2&a[0]&&u(o,1)):(o=ye(l),o.c(),u(o,1),o.m(t,null)):o&&(G(),m(o,1,1,()=>{o=null}),Y()),(!i||3&a[0])&&gt(t,"monaco-diff-container-with-descriptions",l[1].length>0&&l[0])},i(l){i||(u(o),i=!0)},o(l){m(o),i=!1},d(l){l&&g(t),s[24](null),o&&o.d()}}}function _i(s,t,e){let n,i,o;const l=zn();let{originalCode:a=""}=t,{modifiedCode:r=""}=t,{path:c}=t,{descriptions:d=[]}=t,{lineOffset:f=0}=t,{extraPrefixLines:p=[]}=t,{extraSuffixLines:k=[]}=t,{theme:C}=t,{areDescriptionsVisible:v=!0}=t,{isNewFile:F=!1}=t,{isDeletedFile:N=!1}=t;const b=Un.getContext().monaco;let $,y,_,A;Lt(s,b,O=>e(23,n=O));let q,V=[];const P=Wn();let H,Z=Gt(0);Lt(s,Z,O=>e(4,i=O));let Q=F?20*r.split(`
`).length+40:100;const it=n?n.languages.getLanguages().map(O=>O.id):[];function et(O,x){var T,U;if(x){const j=(T=x.split(".").pop())==null?void 0:T.toLowerCase();if(j){const I=(U=n==null?void 0:n.languages.getLanguages().find(J=>{var K;return(K=J.extensions)==null?void 0:K.includes("."+j)}))==null?void 0:U.id;if(I&&it.includes(I))return I}}return"plaintext"}const nt=Gt({});Lt(s,nt,O=>e(5,o=O));let X=null;function ot(){if(!$)return;V=V.filter(T=>(T.dispose(),!1));const O=$.getOriginalEditor(),x=$.getModifiedEditor();V.push(O.onDidScrollChange(()=>{fe(Z,i=O.getScrollTop(),i)}),x.onDidScrollChange(()=>{fe(Z,i=x.getScrollTop(),i)}))}function pt(){if(!$||!q)return;const O=$.getOriginalEditor(),x=$.getModifiedEditor();V.push(x.onDidContentSizeChange(()=>P.requestLayout()),O.onDidContentSizeChange(()=>P.requestLayout()),$.onDidUpdateDiff(()=>P.requestLayout()),x.onDidChangeHiddenAreas(()=>P.requestLayout()),O.onDidChangeHiddenAreas(()=>P.requestLayout()),x.onDidLayoutChange(()=>P.requestLayout()),O.onDidLayoutChange(()=>P.requestLayout()),x.onDidFocusEditorWidget(()=>{Ct(!0)}),O.onDidFocusEditorWidget(()=>{Ct(!0)}),x.onDidBlurEditorWidget(()=>{Ct(!1)}),O.onDidBlurEditorWidget(()=>{Ct(!1)}),x.onDidChangeModelContent(()=>{$t=!0,Dt=Date.now();const T=(A==null?void 0:A.getValue())||"";if(T===r)return;const U=T.replace(p.join(""),"").replace(k.join(""),"");l("codeChange",{modifiedCode:U});const j=setTimeout(()=>{$t=!1},500);V.push({dispose:()=>clearTimeout(j)})})),function(){!q||!$||(X&&clearTimeout(X),X=setTimeout(()=>{if(!q.__hasClickListener){const T=U=>{const j=U.target;j&&(j.closest('[title="Show Unchanged Region"]')||j.closest('[title="Hide Unchanged Region"]'))&&_t()};q.addEventListener("click",T),e(2,q.__hasClickListener=!0,q),V.push({dispose:()=>{q.removeEventListener("click",T)}})}$&&V.push($.onDidUpdateDiff(()=>{_t()}))},300))}()}an(()=>{$==null||$.dispose(),y==null||y.dispose(),_==null||_.dispose(),A==null||A.dispose(),V.forEach(O=>O.dispose()),X&&clearTimeout(X),H==null||H()});let rt=null;function _t(){rt&&clearTimeout(rt),rt=setTimeout(()=>{P.requestLayout(),rt=null},100),rt&&V.push({dispose:()=>{rt&&(clearTimeout(rt),rt=null)}})}function wt(O,x,T,U=[],j=[]){if(!n)return void console.error("Monaco not loaded. Diff view cannot be updated.");_==null||_.dispose(),A==null||A.dispose(),x=x||"",T=T||"";const I=U.join(""),J=j.join("");if(x=F?T.split(`
`).map(()=>" ").join(`
`):I+x+J,T=I+T+J,_=n.editor.createModel(x,void 0,O!==void 0?n.Uri.parse("file://"+O+`#${crypto.randomUUID()}`):void 0),N&&(T=T.split(`
`).map(()=>" ").join(`
`)),e(22,A=n.editor.createModel(T,void 0,O!==void 0?n.Uri.parse("file://"+O+`#${crypto.randomUUID()}`):void 0)),$){$.setModel({original:_,modified:A});const K=$.getOriginalEditor();K&&K.updateOptions({lineNumbers:"off"}),ot(),X&&clearTimeout(X),X=setTimeout(()=>{pt(),X=null},300)}}Zt(()=>{if(n)if(F){e(21,y=n.editor.create(q,{automaticLayout:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},overviewRulerBorder:!1,theme:C,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:U=>`${f-p.length+U}`}));const O=et(0,c);e(22,A=n.editor.createModel(r,O,c!==void 0?n.Uri.parse("file://"+c+`#${crypto.randomUUID()}`):void 0)),y.setModel(A),V.push(y.onDidChangeModelContent(()=>{$t=!0,Dt=Date.now();const U=(A==null?void 0:A.getValue())||"";if(U===r)return;l("codeChange",{modifiedCode:U});const j=setTimeout(()=>{$t=!1},500);V.push({dispose:()=>clearTimeout(j)})})),V.push(y.onDidFocusEditorWidget(()=>{y==null||y.updateOptions({scrollbar:{handleMouseWheel:!0}})}),y.onDidBlurEditorWidget(()=>{y==null||y.updateOptions({scrollbar:{handleMouseWheel:!1}})}));const x=y.getContentHeight();e(3,Q=Math.max(x,60));const T=setTimeout(()=>{y==null||y.layout()},0);V.push({dispose:()=>clearTimeout(T)})}else e(20,$=n.editor.createDiffEditor(q,{automaticLayout:!0,useInlineViewWhenSpaceIsLimited:!0,enableSplitViewResizing:!0,stickyScroll:{enabled:!0},scrollBeyondLastLine:!1,minimap:{enabled:!1},renderOverviewRuler:!1,renderGutterMenu:!1,theme:C,scrollbar:{alwaysConsumeMouseWheel:!1,handleMouseWheel:!1},lineNumbers:O=>`${f-p.length+O}`,hideUnchangedRegions:{enabled:!0,revealLineCount:3,minimumLineCount:3,contextLineCount:3}})),H&&H(),H=P.registerEditor({editor:$,updateHeight:kt,id:`monaco-diff-${crypto.randomUUID().slice(0,8)}`}),wt(c,a,r,p,k),ot(),pt(),X&&clearTimeout(X),X=setTimeout(()=>{P.requestLayout(),X=null},100);else console.error("Monaco not loaded. Diff view cannot be initialized.")});let $t=!1,Dt=0;function mt(O,x=!0){return $?(x?$.getModifiedEditor():$.getOriginalEditor()).getTopForLineNumber(O):18*O}function kt(){if(!$)return;const O=$.getModel(),x=O==null?void 0:O.original,T=O==null?void 0:O.modified;if(!x||!T)return;const U=$.getOriginalEditor(),j=$.getModifiedEditor(),I=$.getLineChanges()||[];let J;if(I.length===0){const K=U.getContentHeight(),B=j.getContentHeight();J=Math.max(100,K,B)}else{let K=0,B=0;for(const ut of I)ut.originalEndLineNumber>0&&(K=Math.max(K,ut.originalEndLineNumber)),ut.modifiedEndLineNumber>0&&(B=Math.max(B,ut.modifiedEndLineNumber));K=Math.min(K+3,x.getLineCount()),B=Math.min(B+3,T.getLineCount());const tt=U.getTopForLineNumber(K),st=j.getTopForLineNumber(B);J=Math.max(tt,st)+60}e(3,Q=Math.min(J,2e4)),$.layout(),St()}function Ct(O){if(!$)return;const x=$.getOriginalEditor(),T=$.getModifiedEditor();x.updateOptions({scrollbar:{handleMouseWheel:O}}),T.updateOptions({scrollbar:{handleMouseWheel:O}})}function W(O){if(!$)return y?y.getTopForLineNumber(O.range.start+1):0;const x=$.getModel(),T=x==null?void 0:x.original,U=x==null?void 0:x.modified;if(!T||!U)return 0;const j=mt(O.range.start+1,!1),I=mt(O.range.start+1,!0);return j&&!I?j:!j&&I?I:Math.min(j,I)}function St(){if(!$&&!y||d.length===0)return;const O={};d.forEach((x,T)=>{O[T]=W(x)}),function(x,T=50){const U=Object.keys(x).sort((j,I)=>x[Number(j)]-x[Number(I)]);for(let j=0;j<U.length-1;j++){const I=Number(U[j]),J=x[I];x[I+1]-J<T&&(x[Number(U[j+1])]=J+T)}}(O),nt.set(O)}const Pt=crypto.randomUUID();return s.$$set=O=>{"originalCode"in O&&e(11,a=O.originalCode),"modifiedCode"in O&&e(12,r=O.modifiedCode),"path"in O&&e(13,c=O.path),"descriptions"in O&&e(1,d=O.descriptions),"lineOffset"in O&&e(14,f=O.lineOffset),"extraPrefixLines"in O&&e(15,p=O.extraPrefixLines),"extraSuffixLines"in O&&e(16,k=O.extraSuffixLines),"theme"in O&&e(17,C=O.theme),"areDescriptionsVisible"in O&&e(0,v=O.areDescriptionsVisible),"isNewFile"in O&&e(18,F=O.isNewFile),"isDeletedFile"in O&&e(19,N=O.isDeletedFile)},s.$$.update=()=>{if(16103424&s.$$.dirty[0]&&(O=r,!($t||Date.now()-Dt<1e3||A&&A.getValue()===p.join("")+O+k.join(""))))if(F&&y){if(A)A.setValue(r);else{const x=et(0,c);n&&e(22,A=n.editor.createModel(r,x,c!==void 0?n.Uri.parse("file://"+c+`#${crypto.randomUUID()}`):void 0)),A&&y.setModel(A)}e(3,Q=20*r.split(`
`).length+40),y.layout()}else!F&&$&&(wt(c,a,r,p,k),P.requestLayout());var O;if(3145730&s.$$.dirty[0]&&($||y)&&d.length>0&&St(),2363392&s.$$.dirty[0]&&F&&r&&y){const x=y.getContentHeight();e(3,Q=Math.max(x,60)),y.layout()}},[v,d,q,Q,i,o,b,Z,nt,W,Pt,a,r,c,f,p,k,C,F,N,$,y,A,n,function(O){Mt[O?"unshift":"push"](()=>{q=O,e(2,q)})},()=>e(0,v=!v)]}class Ci extends vt{constructor(t){super(),yt(this,t,_i,wi,ht,{originalCode:11,modifiedCode:12,path:13,descriptions:1,lineOffset:14,extraPrefixLines:15,extraSuffixLines:16,theme:17,areDescriptionsVisible:0,isNewFile:18,isDeletedFile:19},null,[-1,-1])}}const vn=Symbol("focusedPath");function yn(){return Rt(vn)}function le(s){return`file-diff-${Ot(s)}`}function xi(s){let t,e,n;function i(l){s[41](l)}let o={path:s[3],originalCode:s[0].originalCode,modifiedCode:s[6],theme:s[15],descriptions:s[4],isNewFile:s[21],isDeletedFile:s[20]};return s[1]!==void 0&&(o.areDescriptionsVisible=s[1]),t=new Ci({props:o}),Mt.push(()=>Ht(t,"areDescriptionsVisible",i)),t.$on("codeChange",s[26]),{c(){D(t.$$.fragment)},m(l,a){M(t,l,a),n=!0},p(l,a){const r={};8&a[0]&&(r.path=l[3]),1&a[0]&&(r.originalCode=l[0].originalCode),64&a[0]&&(r.modifiedCode=l[6]),32768&a[0]&&(r.theme=l[15]),16&a[0]&&(r.descriptions=l[4]),2097152&a[0]&&(r.isNewFile=l[21]),1048576&a[0]&&(r.isDeletedFile=l[20]),!e&&2&a[0]&&(e=!0,r.areDescriptionsVisible=l[1],Bt(()=>e=!1)),t.$set(r)},i(l){n||(u(t.$$.fragment,l),n=!0)},o(l){m(t.$$.fragment,l),n=!1},d(l){L(t,l)}}}function bi(s){let t,e,n;return e=new lt({props:{size:1,$$slots:{default:[ki]},$$scope:{ctx:s}}}),{c(){t=z("div"),D(e.$$.fragment),w(t,"class","too-large-message svelte-1536g7w")},m(i,o){h(i,t,o),M(e,t,null),n=!0},p(i,o){const l={};5888&o[0]|16384&o[1]&&(l.$$scope={dirty:o,ctx:i}),e.$set(l)},i(i){n||(u(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){i&&g(t),L(e)}}}function Fi(s){let t,e,n;return e=new lt({props:{$$slots:{default:[Di]},$$scope:{ctx:s}}}),{c(){t=z("div"),D(e.$$.fragment),w(t,"class","binary-file-message svelte-1536g7w")},m(i,o){h(i,t,o),M(e,t,null),n=!0},p(i,o){const l={};2101632&o[0]|16384&o[1]&&(l.$$scope={dirty:o,ctx:i}),e.$set(l)},i(i){n||(u(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){i&&g(t),L(e)}}}function Ai(s){let t,e,n,i;const o=[qi,Ni],l=[];function a(r,c){return r[8]?0:r[6]?1:-1}return~(e=a(s))&&(n=l[e]=o[e](s)),{c(){t=z("div"),n&&n.c(),w(t,"class","image-container svelte-1536g7w")},m(r,c){h(r,t,c),~e&&l[e].m(t,null),i=!0},p(r,c){let d=e;e=a(r),e===d?~e&&l[e].p(r,c):(n&&(G(),m(l[d],1,1,()=>{l[d]=null}),Y()),~e?(n=l[e],n?n.p(r,c):(n=l[e]=o[e](r),n.c()),u(n,1),n.m(t,null)):n=null)},i(r){i||(u(n),i=!0)},o(r){m(n),i=!1},d(r){r&&g(t),~e&&l[e].d()}}}function ki(s){let t,e,n,i,o,l,a,r=ft(s[12])+"",c=(s[8]?s[10]:s[9])+"";return{c(){t=R('File "'),e=R(r),n=R('" is too large to display a diff (size: '),i=R(c),o=R(" bytes, max: "),l=R(Xn),a=R(" bytes).")},m(d,f){h(d,t,f),h(d,e,f),h(d,n,f),h(d,i,f),h(d,o,f),h(d,l,f),h(d,a,f)},p(d,f){4096&f[0]&&r!==(r=ft(d[12])+"")&&ct(e,r),1792&f[0]&&c!==(c=(d[8]?d[10]:d[9])+"")&&ct(i,c)},d(d){d&&(g(t),g(e),g(n),g(i),g(o),g(l),g(a))}}}function zi(s){let t,e,n,i=ft(s[12])+"";return{c(){t=R("Binary file modified: "),e=R(i),n=R(".")},m(o,l){h(o,t,l),h(o,e,l),h(o,n,l)},p(o,l){4096&l[0]&&i!==(i=ft(o[12])+"")&&ct(e,i)},d(o){o&&(g(t),g(e),g(n))}}}function Li(s){let t,e,n,i=ft(s[12])+"";return{c(){t=R("Binary file deleted: "),e=R(i),n=R(".")},m(o,l){h(o,t,l),h(o,e,l),h(o,n,l)},p(o,l){4096&l[0]&&i!==(i=ft(o[12])+"")&&ct(e,i)},d(o){o&&(g(t),g(e),g(n))}}}function Mi(s){let t,e,n,i=ft(s[12])+"";return{c(){t=R("Binary file added: "),e=R(i),n=R(".")},m(o,l){h(o,t,l),h(o,e,l),h(o,n,l)},p(o,l){4096&l[0]&&i!==(i=ft(o[12])+"")&&ct(e,i)},d(o){o&&(g(t),g(e),g(n))}}}function Di(s){let t;function e(o,l){return o[21]||o[7]?Mi:o[8]?Li:zi}let n=e(s),i=n(s);return{c(){i.c(),t=R(`
            No text preview available.`)},m(o,l){i.m(o,l),h(o,t,l)},p(o,l){n===(n=e(o))&&i?i.p(o,l):(i.d(1),i=n(o),i&&(i.c(),i.m(t.parentNode,t)))},d(o){o&&g(t),i.d(o)}}}function Ni(s){let t,e,n,i,o,l,a,r;t=new lt({props:{class:"image-info-text",$$slots:{default:[Pi]},$$scope:{ctx:s}}});let c=s[0].originalCode&&s[6]!==s[0].originalCode&&!s[21]&&_e(s);return{c(){D(t.$$.fragment),e=S(),n=z("img"),l=S(),c&&c.c(),a=xt(),Tt(n.src,i="data:"+s[19]+";base64,"+btoa(s[6]))||w(n,"src",i),w(n,"alt",o="Current "+ft(s[12])),w(n,"class","image-preview svelte-1536g7w")},m(d,f){M(t,d,f),h(d,e,f),h(d,n,f),h(d,l,f),c&&c.m(d,f),h(d,a,f),r=!0},p(d,f){const p={};2101376&f[0]|16384&f[1]&&(p.$$scope={dirty:f,ctx:d}),t.$set(p),(!r||524352&f[0]&&!Tt(n.src,i="data:"+d[19]+";base64,"+btoa(d[6])))&&w(n,"src",i),(!r||4096&f[0]&&o!==(o="Current "+ft(d[12])))&&w(n,"alt",o),d[0].originalCode&&d[6]!==d[0].originalCode&&!d[21]?c?(c.p(d,f),2097217&f[0]&&u(c,1)):(c=_e(d),c.c(),u(c,1),c.m(a.parentNode,a)):c&&(G(),m(c,1,1,()=>{c=null}),Y())},i(d){r||(u(t.$$.fragment,d),u(c),r=!0)},o(d){m(t.$$.fragment,d),m(c),r=!1},d(d){d&&(g(e),g(n),g(l),g(a)),L(t,d),c&&c.d(d)}}}function qi(s){let t,e,n,i;t=new lt({props:{class:"image-info-text",$$slots:{default:[Ti]},$$scope:{ctx:s}}});let o=s[0].originalCode&&Ce(s);return{c(){D(t.$$.fragment),e=S(),o&&o.c(),n=xt()},m(l,a){M(t,l,a),h(l,e,a),o&&o.m(l,a),h(l,n,a),i=!0},p(l,a){const r={};4096&a[0]|16384&a[1]&&(r.$$scope={dirty:a,ctx:l}),t.$set(r),l[0].originalCode?o?(o.p(l,a),1&a[0]&&u(o,1)):(o=Ce(l),o.c(),u(o,1),o.m(n.parentNode,n)):o&&(G(),m(o,1,1,()=>{o=null}),Y())},i(l){i||(u(t.$$.fragment,l),u(o),i=!0)},o(l){m(t.$$.fragment,l),m(o),i=!1},d(l){l&&(g(e),g(n)),L(t,l),o&&o.d(l)}}}function Ei(s){let t;return{c(){t=R("Image modified")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function Oi(s){let t;return{c(){t=R("New image added")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function Pi(s){let t,e,n=ft(s[12])+"";function i(a,r){return a[21]||a[7]?Oi:Ei}let o=i(s),l=o(s);return{c(){l.c(),t=R(": "),e=R(n)},m(a,r){l.m(a,r),h(a,t,r),h(a,e,r)},p(a,r){o!==(o=i(a))&&(l.d(1),l=o(a),l&&(l.c(),l.m(t.parentNode,t))),4096&r[0]&&n!==(n=ft(a[12])+"")&&ct(e,n)},d(a){a&&(g(t),g(e)),l.d(a)}}}function _e(s){let t,e,n,i,o,l;return t=new lt({props:{class:"image-info-text",$$slots:{default:[Ri]},$$scope:{ctx:s}}}),{c(){D(t.$$.fragment),e=S(),n=z("img"),Tt(n.src,i="data:"+Ut(s[3])+";base64,"+btoa(s[0].originalCode))||w(n,"src",i),w(n,"alt",o="Original "+ft(s[12])),w(n,"class","image-preview image-preview--previous svelte-1536g7w")},m(a,r){M(t,a,r),h(a,e,r),h(a,n,r),l=!0},p(a,r){const c={};16384&r[1]&&(c.$$scope={dirty:r,ctx:a}),t.$set(c),(!l||9&r[0]&&!Tt(n.src,i="data:"+Ut(a[3])+";base64,"+btoa(a[0].originalCode)))&&w(n,"src",i),(!l||4096&r[0]&&o!==(o="Original "+ft(a[12])))&&w(n,"alt",o)},i(a){l||(u(t.$$.fragment,a),l=!0)},o(a){m(t.$$.fragment,a),l=!1},d(a){a&&(g(e),g(n)),L(t,a)}}}function Ri(s){let t;return{c(){t=R("Previous version:")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function Ti(s){let t,e,n=ft(s[12])+"";return{c(){t=R("Image deleted: "),e=R(n)},m(i,o){h(i,t,o),h(i,e,o)},p(i,o){4096&o[0]&&n!==(n=ft(i[12])+"")&&ct(e,n)},d(i){i&&(g(t),g(e))}}}function Ce(s){let t,e,n,i,o,l;return t=new lt({props:{class:"image-info-text",$$slots:{default:[Vi]},$$scope:{ctx:s}}}),{c(){D(t.$$.fragment),e=S(),n=z("img"),Tt(n.src,i="data:"+Ut(s[3])+";base64,"+btoa(s[0].originalCode))||w(n,"src",i),w(n,"alt",o="Original "+ft(s[12])),w(n,"class","image-preview svelte-1536g7w")},m(a,r){M(t,a,r),h(a,e,r),h(a,n,r),l=!0},p(a,r){const c={};16384&r[1]&&(c.$$scope={dirty:r,ctx:a}),t.$set(c),(!l||9&r[0]&&!Tt(n.src,i="data:"+Ut(a[3])+";base64,"+btoa(a[0].originalCode)))&&w(n,"src",i),(!l||4096&r[0]&&o!==(o="Original "+ft(a[12])))&&w(n,"alt",o)},i(a){l||(u(t.$$.fragment,a),l=!0)},o(a){m(t.$$.fragment,a),l=!1},d(a){a&&(g(e),g(n)),L(t,a)}}}function Vi(s){let t;return{c(){t=R("Previous version:")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function Si(s){let t,e,n,i;const o=[Ai,Fi,bi,xi],l=[];function a(r,c){return r[18]?0:r[17]?1:r[16]?2:3}return e=a(s),n=l[e]=o[e](s),{c(){t=z("div"),n.c(),w(t,"class","changes svelte-1536g7w")},m(r,c){h(r,t,c),l[e].m(t,null),i=!0},p(r,c){let d=e;e=a(r),e===d?l[e].p(r,c):(G(),m(l[d],1,1,()=>{l[d]=null}),Y(),n=l[e],n?n.p(r,c):(n=l[e]=o[e](r),n.c()),u(n,1),n.m(t,null))},i(r){i||(u(n),i=!0)},o(r){m(n),i=!1},d(r){r&&g(t),l[e].d()}}}function ji(s){let t,e=ft(s[12])+"";return{c(){t=R(e)},m(n,i){h(n,t,i)},p(n,i){4096&i[0]&&e!==(e=ft(n[12])+"")&&ct(t,e)},d(n){n&&g(t)}}}function Hi(s){let t,e;return t=new Ft({props:{variant:"ghost-block",color:"neutral",size:1,class:"c-codeblock__filename",$$slots:{default:[ji]},$$scope:{ctx:s}}}),t.$on("click",s[28]),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};4096&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:n}),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function xe(s){let t,e,n=Kt(s[12])+"";return{c(){t=z("span"),e=R(n),w(t,"class","c-directory svelte-1536g7w")},m(i,o){h(i,t,o),E(t,e)},p(i,o){4096&o[0]&&n!==(n=Kt(i[12])+"")&&ct(e,n)},d(i){i&&g(t)}}}function Bi(s){let t,e,n,i=s[23]>0&&be(s),o=s[22]>0&&Fe(s);return{c(){t=z("div"),i&&i.c(),e=S(),o&&o.c(),w(t,"class","changes-indicator svelte-1536g7w")},m(l,a){h(l,t,a),i&&i.m(t,null),E(t,e),o&&o.m(t,null),n=!0},p(l,a){l[23]>0?i?(i.p(l,a),8388608&a[0]&&u(i,1)):(i=be(l),i.c(),u(i,1),i.m(t,e)):i&&(G(),m(i,1,1,()=>{i=null}),Y()),l[22]>0?o?(o.p(l,a),4194304&a[0]&&u(o,1)):(o=Fe(l),o.c(),u(o,1),o.m(t,null)):o&&(G(),m(o,1,1,()=>{o=null}),Y())},i(l){n||(u(i),u(o),n=!0)},o(l){m(i),m(o),n=!1},d(l){l&&g(t),i&&i.d(),o&&o.d()}}}function Ui(s){let t;return{c(){t=z("span"),t.textContent="New File",w(t,"class","new-file-badge svelte-1536g7w")},m(e,n){h(e,t,n)},p:at,i:at,o:at,d(e){e&&g(t)}}}function be(s){let t,e,n;return e=new lt({props:{size:1,$$slots:{default:[Zi]},$$scope:{ctx:s}}}),{c(){t=z("span"),D(e.$$.fragment),w(t,"class","additions svelte-1536g7w")},m(i,o){h(i,t,o),M(e,t,null),n=!0},p(i,o){const l={};8388608&o[0]|16384&o[1]&&(l.$$scope={dirty:o,ctx:i}),e.$set(l)},i(i){n||(u(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){i&&g(t),L(e)}}}function Zi(s){let t,e;return{c(){t=R("+"),e=R(s[23])},m(n,i){h(n,t,i),h(n,e,i)},p(n,i){8388608&i[0]&&ct(e,n[23])},d(n){n&&(g(t),g(e))}}}function Fe(s){let t,e,n;return e=new lt({props:{size:1,$$slots:{default:[Ii]},$$scope:{ctx:s}}}),{c(){t=z("span"),D(e.$$.fragment),w(t,"class","deletions svelte-1536g7w")},m(i,o){h(i,t,o),M(e,t,null),n=!0},p(i,o){const l={};4194304&o[0]|16384&o[1]&&(l.$$scope={dirty:o,ctx:i}),e.$set(l)},i(i){n||(u(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){i&&g(t),L(e)}}}function Ii(s){let t,e;return{c(){t=R("-"),e=R(s[22])},m(n,i){h(n,t,i),h(n,e,i)},p(n,i){4194304&i[0]&&ct(e,n[22])},d(n){n&&(g(t),g(e))}}}function Wi(s){let t;return{c(){t=R("Apply")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function Ji(s){let t;return{c(){t=R("Applied")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function Gi(s){let t,e,n;return e=new te({}),{c(){t=z("div"),D(e.$$.fragment),w(t,"class","applied__icon svelte-1536g7w")},m(i,o){h(i,t,o),M(e,t,null),n=!0},i(i){n||(u(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){i&&g(t),L(e)}}}function Yi(s){let t,e,n;return e=new de({props:{iconName:"check"}}),{c(){t=z("div"),D(e.$$.fragment),w(t,"class","applied svelte-1536g7w")},m(i,o){h(i,t,o),M(e,t,null),n=!0},i(i){n||(u(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){i&&g(t),L(e)}}}function Ki(s){let t,e,n,i,o;function l(p,k){return p[5]?Ji:Wi}let a=l(s),r=a(s);const c=[Yi,Gi],d=[];function f(p,k){return p[5]?0:1}return e=f(s),n=d[e]=c[e](s),{c(){r.c(),t=S(),n.c(),i=xt()},m(p,k){r.m(p,k),h(p,t,k),d[e].m(p,k),h(p,i,k),o=!0},p(p,k){a!==(a=l(p))&&(r.d(1),r=a(p),r&&(r.c(),r.m(t.parentNode,t)));let C=e;e=f(p),e!==C&&(G(),m(d[C],1,1,()=>{d[C]=null}),Y(),n=d[e],n||(n=d[e]=c[e](p),n.c()),u(n,1),n.m(i.parentNode,i))},i(p){o||(u(n),o=!0)},o(p){m(n),o=!1},d(p){p&&(g(t),g(i)),r.d(p),d[e].d(p)}}}function Qi(s){let t,e;return t=new Ft({props:{variant:"ghost-block",color:"neutral",size:2,disabled:s[14],$$slots:{default:[Ki]},$$scope:{ctx:s}}}),t.$on("click",s[27]),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};16384&i[0]&&(o.disabled=n[14]),32&i[0]|16384&i[1]&&(o.$$scope={dirty:i,ctx:n}),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function Ae(s){let t,e;return t=new Et({props:{content:s[11],triggerOn:[Yt.Hover],delayDurationMs:300,$$slots:{default:[ts]},$$scope:{ctx:s}}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};2048&i[0]&&(o.content=n[11]),16384&i[1]&&(o.$$scope={dirty:i,ctx:n}),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function Xi(s){let t,e;return t=new $n({}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function ts(s){let t,e;return t=new ae({props:{size:1,variant:"ghost",color:"neutral",$$slots:{default:[Xi]},$$scope:{ctx:s}}}),t.$on("click",s[28]),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};16384&i[1]&&(o.$$scope={dirty:i,ctx:n}),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function es(s){let t,e,n,i,o,l,a,r,c,d,f,p,k,C=Kt(s[12]);e=new Hn({}),o=new Et({props:{content:s[11],triggerOn:[Yt.Hover],delayDurationMs:300,$$slots:{default:[Hi]},$$scope:{ctx:s}}});let v=C&&xe(s);const F=[Ui,Bi],N=[];function b(y,_){return y[21]?0:1}r=b(s),c=N[r]=F[r](s),f=new Et({props:{content:s[13],triggerOn:[Yt.Hover],delayDurationMs:300,$$slots:{default:[Qi]},$$scope:{ctx:s}}});let $=s[5]&&Ae(s);return{c(){t=z("div"),D(e.$$.fragment),n=S(),i=z("div"),D(o.$$.fragment),l=S(),v&&v.c(),a=S(),c.c(),d=S(),D(f.$$.fragment),p=S(),$&&$.c(),w(i,"class","c-path svelte-1536g7w"),w(t,"slot","header"),w(t,"class","header svelte-1536g7w")},m(y,_){h(y,t,_),M(e,t,null),E(t,n),E(t,i),M(o,i,null),E(i,l),v&&v.m(i,null),E(t,a),N[r].m(t,null),E(t,d),M(f,t,null),E(t,p),$&&$.m(t,null),k=!0},p(y,_){const A={};2048&_[0]&&(A.content=y[11]),4096&_[0]|16384&_[1]&&(A.$$scope={dirty:_,ctx:y}),o.$set(A),4096&_[0]&&(C=Kt(y[12])),C?v?v.p(y,_):(v=xe(y),v.c(),v.m(i,null)):v&&(v.d(1),v=null);let q=r;r=b(y),r===q?N[r].p(y,_):(G(),m(N[q],1,1,()=>{N[q]=null}),Y(),c=N[r],c?c.p(y,_):(c=N[r]=F[r](y),c.c()),u(c,1),c.m(t,d));const V={};8192&_[0]&&(V.content=y[13]),16416&_[0]|16384&_[1]&&(V.$$scope={dirty:_,ctx:y}),f.$set(V),y[5]?$?($.p(y,_),32&_[0]&&u($,1)):($=Ae(y),$.c(),u($,1),$.m(t,null)):$&&(G(),m($,1,1,()=>{$=null}),Y())},i(y){k||(u(e.$$.fragment,y),u(o.$$.fragment,y),u(c),u(f.$$.fragment,y),u($),k=!0)},o(y){m(e.$$.fragment,y),m(o.$$.fragment,y),m(c),m(f.$$.fragment,y),m($),k=!1},d(y){y&&g(t),L(e),L(o),v&&v.d(),N[r].d(),L(f),$&&$.d()}}}function ns(s){let t,e,n,i,o;function l(r){s[42](r)}let a={stickyHeader:!0,$$slots:{header:[es],default:[Si]},$$scope:{ctx:s}};return s[2]!==void 0&&(a.collapsed=s[2]),e=new jn({props:a}),Mt.push(()=>Ht(e,"collapsed",l)),{c(){t=z("div"),D(e.$$.fragment),w(t,"class","c svelte-1536g7w"),w(t,"id",i=le(s[3])),gt(t,"focused",s[24]===s[3])},m(r,c){h(r,t,c),M(e,t,null),o=!0},p(r,c){const d={};16777211&c[0]|16384&c[1]&&(d.$$scope={dirty:c,ctx:r}),!n&&4&c[0]&&(n=!0,d.collapsed=r[2],Bt(()=>n=!1)),e.$set(d),(!o||8&c[0]&&i!==(i=le(r[3])))&&w(t,"id",i),(!o||16777224&c[0])&&gt(t,"focused",r[24]===r[3])},i(r){o||(u(e.$$.fragment,r),o=!0)},o(r){m(e.$$.fragment,r),o=!1},d(r){r&&g(t),L(e)}}}function is(s,t,e){let n,i,o,l,a,r,c,d,f,p,k,C,v,F,N,b,$,y,_,A,q,V,P;Lt(s,Ln,W=>e(40,V=W));let{path:H}=t,{change:Z}=t,{descriptions:Q=[]}=t,{areDescriptionsVisible:it=!0}=t,{isExpandedDefault:et}=t,{isCollapsed:nt=!et}=t,{isApplying:X}=t,{hasApplied:ot}=t,{onApplyChanges:pt}=t,{onCodeChange:rt}=t,{onOpenFile:_t}=t,{isAgentFromDifferentRepo:wt=!1}=t;const $t=yn();Lt(s,$t,W=>e(24,P=W));const Dt=Rt(Vt.key);let mt=Z.modifiedCode,kt=_;function Ct(){e(11,kt=`Open ${_??"file"}`)}return Zt(()=>{Ct()}),s.$$set=W=>{"path"in W&&e(3,H=W.path),"change"in W&&e(0,Z=W.change),"descriptions"in W&&e(4,Q=W.descriptions),"areDescriptionsVisible"in W&&e(1,it=W.areDescriptionsVisible),"isExpandedDefault"in W&&e(29,et=W.isExpandedDefault),"isCollapsed"in W&&e(2,nt=W.isCollapsed),"isApplying"in W&&e(30,X=W.isApplying),"hasApplied"in W&&e(5,ot=W.hasApplied),"onApplyChanges"in W&&e(31,pt=W.onApplyChanges),"onCodeChange"in W&&e(32,rt=W.onCodeChange),"onOpenFile"in W&&e(33,_t=W.onOpenFile),"isAgentFromDifferentRepo"in W&&e(34,wt=W.isAgentFromDifferentRepo)},s.$$.update=()=>{var W;1&s.$$.dirty[0]&&e(6,mt=Z.modifiedCode),1&s.$$.dirty[0]&&e(39,n=On(Z.diff)),256&s.$$.dirty[1]&&e(23,i=n.additions),256&s.$$.dirty[1]&&e(22,o=n.deletions),1&s.$$.dirty[0]&&e(21,l=Pn(Z)),1&s.$$.dirty[0]&&e(20,a=Rn(Z)),8&s.$$.dirty[0]&&e(38,r=Yn(H)),8&s.$$.dirty[0]&&e(19,c=Ut(H)),8&s.$$.dirty[0]&&e(37,d=Kn(H)),1&s.$$.dirty[0]&&e(10,f=((W=Z.originalCode)==null?void 0:W.length)||0),64&s.$$.dirty[0]&&e(9,p=(mt==null?void 0:mt.length)||0),1024&s.$$.dirty[0]&&e(36,k=me(f)),512&s.$$.dirty[0]&&e(35,C=me(p)),65&s.$$.dirty[0]&&e(8,v=!mt&&!!Z.originalCode),65&s.$$.dirty[0]&&e(7,F=!!mt&&!Z.originalCode),128&s.$$.dirty[1]&&e(18,N=r),192&s.$$.dirty[1]&&e(17,b=!r&&d),384&s.$$.dirty[0]|240&s.$$.dirty[1]&&e(16,$=!r&&!d&&(C||v&&k||F&&C)),512&s.$$.dirty[1]&&e(15,y=Zn(V==null?void 0:V.category,V==null?void 0:V.intensity)),8&s.$$.dirty[0]&&e(12,_=Qn(H)),1073741824&s.$$.dirty[0]|8&s.$$.dirty[1]&&e(14,A=X||wt),1073741856&s.$$.dirty[0]|8&s.$$.dirty[1]&&e(13,q=X?"Applying changes...":ot?"Reapply changes to local file":wt?"Cannot apply changes from a different repository locally":"Apply changes to local file")},[Z,it,nt,H,Q,ot,mt,F,v,p,f,kt,_,q,A,y,$,b,N,c,a,l,o,i,P,$t,function(W){e(6,mt=W.detail.modifiedCode),rt==null||rt(mt)},function(){Dt.reportApplyChangesEvent(),e(0,Z.modifiedCode=mt,Z),rt==null||rt(mt),pt==null||pt()},async function(){_t&&(e(11,kt="Opening file..."),await _t()?Ct():(e(11,kt="Failed to open file. Does the file exist?"),setTimeout(()=>{Ct()},2e3)))},et,X,pt,rt,_t,wt,C,k,d,r,n,V,function(W){it=W,e(1,it)},function(W){nt=W,e(2,nt)}]}class wn extends vt{constructor(t){super(),yt(this,t,is,ns,ht,{path:3,change:0,descriptions:4,areDescriptionsVisible:1,isExpandedDefault:29,isCollapsed:2,isApplying:30,hasApplied:5,onApplyChanges:31,onCodeChange:32,onOpenFile:33,isAgentFromDifferentRepo:34},null,[-1,-1])}}function ke(s,t,e){const n=s.slice();return n[6]=t[e],n}function ss(s){let t,e;return t=new gn({props:{filename:s[0].name}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};1&i&&(o.filename=n[0].name),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function os(s){let t,e;return t=new ce({props:{icon:s[0].isExpanded?"chevron-down":"chevron-right"}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};1&i&&(o.icon=n[0].isExpanded?"chevron-down":"chevron-right"),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function ls(s){let t,e,n=(s[0].displayName||s[0].name)+"";return{c(){t=z("span"),e=R(n),w(t,"class","full-path-text svelte-qnxoj")},m(i,o){h(i,t,o),E(t,e)},p(i,o){1&o&&n!==(n=(i[0].displayName||i[0].name)+"")&&ct(e,n)},d(i){i&&g(t)}}}function ze(s){let t,e,n=dt(Array.from(s[0].children.values()).sort(Me)),i=[];for(let l=0;l<n.length;l+=1)i[l]=Le(ke(s,n,l));const o=l=>m(i[l],1,1,()=>{i[l]=null});return{c(){t=z("div");for(let l=0;l<i.length;l+=1)i[l].c();w(t,"class","tree-node__children svelte-qnxoj"),w(t,"role","group")},m(l,a){h(l,t,a);for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(t,null);e=!0},p(l,a){if(3&a){let r;for(n=dt(Array.from(l[0].children.values()).sort(Me)),r=0;r<n.length;r+=1){const c=ke(l,n,r);i[r]?(i[r].p(c,a),u(i[r],1)):(i[r]=Le(c),i[r].c(),u(i[r],1),i[r].m(t,null))}for(G(),r=n.length;r<i.length;r+=1)o(r);Y()}},i(l){if(!e){for(let a=0;a<n.length;a+=1)u(i[a]);e=!0}},o(l){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)m(i[a]);e=!1},d(l){l&&g(t),At(i,l)}}}function Le(s){let t,e;return t=new _n({props:{node:s[6],indentLevel:s[1]+1}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};1&i&&(o.node=n[6]),2&i&&(o.indentLevel=n[1]+1),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function rs(s){let t,e,n,i,o,l,a,r,c,d,f,p,k,C,v,F,N;const b=[os,ss],$=[];function y(A,q){return A[0].isFile?1:0}l=y(s),a=$[l]=b[l](s),d=new lt({props:{size:1,$$slots:{default:[ls]},$$scope:{ctx:s}}});let _=!s[0].isFile&&s[0].isExpanded&&s[0].children.size>0&&ze(s);return{c(){t=z("div"),e=z("div"),n=z("div"),i=S(),o=z("div"),a.c(),r=S(),c=z("span"),D(d.$$.fragment),C=S(),_&&_.c(),w(n,"class","tree-node__indent svelte-qnxoj"),qt(n,"width",6*s[1]+"px"),w(o,"class","tree-node__icon-container svelte-qnxoj"),w(c,"class","tree-node__label svelte-qnxoj"),w(c,"title",f=s[0].displayName||s[0].name),gt(c,"full-path",s[0].displayName),w(e,"class","tree-node__content svelte-qnxoj"),w(e,"role","treeitem"),w(e,"tabindex","0"),w(e,"aria-selected",p=s[0].path===s[2]),w(e,"aria-expanded",k=s[0].isFile?void 0:s[0].isExpanded),gt(e,"selected",s[0].path===s[2]),gt(e,"collapsed-folder",s[0].displayName&&!s[0].isFile),w(t,"class","tree-node svelte-qnxoj")},m(A,q){h(A,t,q),E(t,e),E(e,n),E(e,i),E(e,o),$[l].m(o,null),E(e,r),E(e,c),M(d,c,null),E(t,C),_&&_.m(t,null),v=!0,F||(N=[jt(e,"click",s[4]),jt(e,"keydown",s[5])],F=!0)},p(A,[q]){(!v||2&q)&&qt(n,"width",6*A[1]+"px");let V=l;l=y(A),l===V?$[l].p(A,q):(G(),m($[V],1,1,()=>{$[V]=null}),Y(),a=$[l],a?a.p(A,q):(a=$[l]=b[l](A),a.c()),u(a,1),a.m(o,null));const P={};513&q&&(P.$$scope={dirty:q,ctx:A}),d.$set(P),(!v||1&q&&f!==(f=A[0].displayName||A[0].name))&&w(c,"title",f),(!v||1&q)&&gt(c,"full-path",A[0].displayName),(!v||5&q&&p!==(p=A[0].path===A[2]))&&w(e,"aria-selected",p),(!v||1&q&&k!==(k=A[0].isFile?void 0:A[0].isExpanded))&&w(e,"aria-expanded",k),(!v||5&q)&&gt(e,"selected",A[0].path===A[2]),(!v||1&q)&&gt(e,"collapsed-folder",A[0].displayName&&!A[0].isFile),!A[0].isFile&&A[0].isExpanded&&A[0].children.size>0?_?(_.p(A,q),1&q&&u(_,1)):(_=ze(A),_.c(),u(_,1),_.m(t,null)):_&&(G(),m(_,1,1,()=>{_=null}),Y())},i(A){v||(u(a),u(d.$$.fragment,A),u(_),v=!0)},o(A){m(a),m(d.$$.fragment,A),m(_),v=!1},d(A){A&&g(t),$[l].d(),L(d),_&&_.d(),F=!1,rn(N)}}}const Me=(s,t)=>s.isFile===t.isFile?s.name.localeCompare(t.name):s.isFile?1:-1;function as(s,t,e){let n,{node:i}=t,{indentLevel:o=0}=t;const l=yn();function a(){i.isFile?l.set(i.path):e(0,i.isExpanded=!i.isExpanded,i)}return Lt(s,l,r=>e(2,n=r)),s.$$set=r=>{"node"in r&&e(0,i=r.node),"indentLevel"in r&&e(1,o=r.indentLevel)},[i,o,n,l,a,r=>r.key==="Enter"&&a()]}class _n extends vt{constructor(t){super(),yt(this,t,as,rs,ht,{node:0,indentLevel:1})}}function De(s,t,e){const n=s.slice();return n[4]=t[e],n}function cs(s){let t,e,n=dt(Array.from(s[1].children.values()).sort(qe)),i=[];for(let l=0;l<n.length;l+=1)i[l]=Ne(De(s,n,l));const o=l=>m(i[l],1,1,()=>{i[l]=null});return{c(){for(let l=0;l<i.length;l+=1)i[l].c();t=xt()},m(l,a){for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(l,a);h(l,t,a),e=!0},p(l,a){if(2&a){let r;for(n=dt(Array.from(l[1].children.values()).sort(qe)),r=0;r<n.length;r+=1){const c=De(l,n,r);i[r]?(i[r].p(c,a),u(i[r],1)):(i[r]=Ne(c),i[r].c(),u(i[r],1),i[r].m(t.parentNode,t))}for(G(),r=n.length;r<i.length;r+=1)o(r);Y()}},i(l){if(!e){for(let a=0;a<n.length;a+=1)u(i[a]);e=!0}},o(l){i=i.filter(Boolean);for(let a=0;a<i.length;a+=1)m(i[a]);e=!1},d(l){l&&g(t),At(i,l)}}}function ds(s){let t,e,n;return e=new lt({props:{size:1,color:"neutral",$$slots:{default:[ps]},$$scope:{ctx:s}}}),{c(){t=z("div"),D(e.$$.fragment),w(t,"class","tree-view__empty svelte-1tnd9l7")},m(i,o){h(i,t,o),M(e,t,null),n=!0},p(i,o){const l={};128&o&&(l.$$scope={dirty:o,ctx:i}),e.$set(l)},i(i){n||(u(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){i&&g(t),L(e)}}}function fs(s){let t;return{c(){t=z("div"),t.innerHTML='<div class="tree-view__skeleton svelte-1tnd9l7"><div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="margin-left: 12px;"></div> <div class="tree-view__skeleton-item svelte-1tnd9l7" style="width: 70%;"></div></div>',w(t,"class","tree-view__loading svelte-1tnd9l7")},m(e,n){h(e,t,n)},p:at,i:at,o:at,d(e){e&&g(t)}}}function Ne(s){let t,e;return t=new _n({props:{node:s[4],indentLevel:0}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};2&i&&(o.node=n[4]),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function ps(s){let t;return{c(){t=R("No changed files")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function us(s){let t,e,n,i,o;const l=[fs,ds,cs],a=[];function r(c,d){return c[0]?0:c[1].children.size===0?1:2}return n=r(s),i=a[n]=l[n](s),{c(){t=z("div"),e=z("div"),i.c(),w(e,"class","tree-view__content svelte-1tnd9l7"),w(e,"role","tree"),w(e,"aria-label","Changed Files"),w(t,"class","tree-view svelte-1tnd9l7")},m(c,d){h(c,t,d),E(t,e),a[n].m(e,null),o=!0},p(c,[d]){let f=n;n=r(c),n===f?a[n].p(c,d):(G(),m(a[f],1,1,()=>{a[f]=null}),Y(),i=a[n],i?i.p(c,d):(i=a[n]=l[n](c),i.c()),u(i,1),i.m(e,null))},i(c){o||(u(i),o=!0)},o(c){m(i),o=!1},d(c){c&&g(t),a[n].d()}}}function re(s,t=!1){if(s.isFile)return;let e="";t&&(e=function(l){let a=l.path.split("/"),r=l;for(;;){const c=Array.from(r.children.values()).filter(f=>!f.isFile),d=Array.from(r.children.values()).filter(f=>f.isFile);if(c.length!==1||d.length!==0)break;r=c[0],a.push(r.name)}return a.join("/")}(s));const n=Array.from(s.children.values()).filter(l=>!l.isFile);for(const l of n)re(l);const i=Array.from(s.children.values()).filter(l=>!l.isFile),o=Array.from(s.children.values()).filter(l=>l.isFile);if(i.length===1&&o.length===0){const l=i[0],a=l.name;if(t){s.displayName=e||`${s.name}/${a}`;for(const[r,c]of l.children.entries()){const d=`${r}`;s.children.set(d,c)}s.children.delete(a)}else{s.displayName?l.displayName=`${s.displayName}/${a}`:l.displayName=`${s.name}/${a}`;for(const[r,c]of l.children.entries()){const d=`${a}/${r}`;s.children.set(d,c)}s.children.delete(a)}}}const qe=(s,t)=>s.isFile===t.isFile?s.name.localeCompare(t.name):s.isFile?1:-1;function $s(s,t,e){let n,{changedFiles:i=[]}=t,{isLoading:o=!1}=t;function l(a){const r={name:"",path:"",isFile:!1,children:new Map,isExpanded:!0};return a.forEach(c=>{const d=c.change_type===ti.deleted?c.old_path:c.new_path;d&&function(f,p){const k=p.split("/");let C=f;for(let v=0;v<k.length;v++){const F=k[v],N=v===k.length-1,b=k.slice(0,v+1).join("/");C.children.has(F)||C.children.set(F,{name:F,path:b,isFile:N,children:new Map,isExpanded:!0}),C=C.children.get(F)}}(r,d)}),function(c){if(!c.isFile)if(c.path!=="")re(c);else{const d=Array.from(c.children.values()).filter(f=>!f.isFile);for(const f of d)re(f,!0)}}(r),r}return s.$$set=a=>{"changedFiles"in a&&e(2,i=a.changedFiles),"isLoading"in a&&e(0,o=a.isLoading)},s.$$.update=()=>{4&s.$$.dirty&&e(1,n=l(i))},[o,n,i]}class Cn extends vt{constructor(t){super(),yt(this,t,$s,us,ht,{changedFiles:2,isLoading:0})}}function Ee(s,t,e){const n=s.slice();return n[19]=t[e],n}function ms(s){let t;return{c(){t=R("Changed files")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function gs(s){let t,e,n;return e=new lt({props:{size:1,color:"neutral",$$slots:{default:[vs]},$$scope:{ctx:s}}}),{c(){t=z("div"),D(e.$$.fragment),w(t,"class","c-edits-list c-edits-list--empty svelte-6iqvaj")},m(i,o){h(i,t,o),M(e,t,null),n=!0},p(i,o){const l={};4194304&o&&(l.$$scope={dirty:o,ctx:i}),e.$set(l)},i(i){n||(u(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){i&&g(t),L(e)}}}function hs(s){let t,e,n,i,o,l,a=[],r=new Map,c=s[9].length>0&&Oe(s),d=dt(s[9]);const f=p=>p[19].qualifiedPathName.relPath;for(let p=0;p<d.length;p+=1){let k=Ee(s,d,p),C=f(k);r.set(C,a[p]=Pe(C,k))}return{c(){t=z("div"),e=z("div"),c&&c.c(),n=S(),i=z("div"),o=z("div");for(let p=0;p<a.length;p+=1)a[p].c();w(e,"class","c-edits-list-controls svelte-6iqvaj"),w(t,"class","c-edits-list-header svelte-6iqvaj"),w(o,"class","c-edits-section svelte-6iqvaj"),w(i,"class","c-edits-list svelte-6iqvaj")},m(p,k){h(p,t,k),E(t,e),c&&c.m(e,null),h(p,n,k),h(p,i,k),E(i,o);for(let C=0;C<a.length;C+=1)a[C]&&a[C].m(o,null);l=!0},p(p,k){p[9].length>0?c?(c.p(p,k),512&k&&u(c,1)):(c=Oe(p),c.c(),u(c,1),c.m(e,null)):c&&(G(),m(c,1,1,()=>{c=null}),Y()),2654&k&&(d=dt(p[9]),G(),a=fn(a,k,f,1,p,d,r,o,pn,Pe,null,Ee),Y())},i(p){if(!l){u(c);for(let k=0;k<d.length;k+=1)u(a[k]);l=!0}},o(p){m(c);for(let k=0;k<a.length;k+=1)m(a[k]);l=!1},d(p){p&&(g(t),g(n),g(i)),c&&c.d();for(let k=0;k<a.length;k+=1)a[k].d()}}}function vs(s){let t;return{c(){t=R("No changes to show")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function Oe(s){let t,e;return t=new Ft({props:{variant:"ghost-block",color:"neutral",size:2,disabled:s[7]||s[8]||s[3].length>0||!s[10],$$slots:{default:[Cs]},$$scope:{ctx:s}}}),t.$on("click",s[12]),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};1416&i&&(o.disabled=n[7]||n[8]||n[3].length>0||!n[10]),4194688&i&&(o.$$scope={dirty:i,ctx:n}),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function ys(s){let t;return{c(){t=R("Apply all")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function ws(s){let t;return{c(){t=R("All applied")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function _s(s){let t;return{c(){t=R("Applying...")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function Cs(s){let t,e,n,i;function o(r,c){return r[7]?_s:r[8]?ws:ys}let l=o(s),a=l(s);return n=new te({}),{c(){a.c(),t=S(),e=z("div"),D(n.$$.fragment),w(e,"class","c-edits-list-controls__icon svelte-6iqvaj")},m(r,c){a.m(r,c),h(r,t,c),h(r,e,c),M(n,e,null),i=!0},p(r,c){l!==(l=o(r))&&(a.d(1),a=l(r),a&&(a.c(),a.m(t.parentNode,t)))},i(r){i||(u(n.$$.fragment,r),i=!0)},o(r){m(n.$$.fragment,r),i=!1},d(r){r&&(g(t),g(e)),a.d(r),L(n)}}}function Pe(s,t){let e,n,i,o,l;function a(...d){return t[16](t[19],...d)}function r(){return t[17](t[19])}function c(){return t[18](t[19])}return n=new wn({props:{path:t[19].qualifiedPathName.relPath,change:t[19].diff,isApplying:t[3].includes(t[19].qualifiedPathName.relPath),hasApplied:t[4].includes(t[19].qualifiedPathName.relPath),onCodeChange:a,onApplyChanges:r,onOpenFile:t[2]?c:void 0,isExpandedDefault:!0}}),{key:s,first:null,c(){e=z("div"),D(n.$$.fragment),i=S(),w(e,"class",""),this.first=e},m(d,f){h(d,e,f),M(n,e,null),E(e,i),l=!0},p(d,f){t=d;const p={};512&f&&(p.path=t[19].qualifiedPathName.relPath),512&f&&(p.change=t[19].diff),520&f&&(p.isApplying=t[3].includes(t[19].qualifiedPathName.relPath)),528&f&&(p.hasApplied=t[4].includes(t[19].qualifiedPathName.relPath)),512&f&&(p.onCodeChange=a),578&f&&(p.onApplyChanges=r),516&f&&(p.onOpenFile=t[2]?c:void 0),n.$set(p)},i(d){l||(u(n.$$.fragment,d),d&&Mn(()=>{l&&(o||(o=pe(e,$e,{},!0)),o.run(1))}),l=!0)},o(d){m(n.$$.fragment,d),d&&(o||(o=pe(e,$e,{},!1)),o.run(0)),l=!1},d(d){d&&g(e),L(n),d&&o&&o.end()}}}function xs(s){let t,e,n,i,o,l,a,r,c,d,f,p;o=new lt({props:{size:1,class:"c-file-explorer__tree__header__label",$$slots:{default:[ms]},$$scope:{ctx:s}}}),a=new Cn({props:{changedFiles:s[0],isLoading:s[5]}});const k=[hs,gs],C=[];function v(F,N){return F[9].length>0?0:1}return d=v(s),f=C[d]=k[d](s),{c(){t=z("div"),e=z("div"),n=z("div"),i=z("div"),D(o.$$.fragment),l=S(),D(a.$$.fragment),r=S(),c=z("div"),f.c(),w(i,"class","c-file-explorer__tree__header svelte-6iqvaj"),w(n,"class","c-file-explorer__tree svelte-6iqvaj"),w(c,"class","c-file-explorer__details svelte-6iqvaj"),w(e,"class","c-file-explorer__layout svelte-6iqvaj"),w(t,"class","c-edits-list-container svelte-6iqvaj")},m(F,N){h(F,t,N),E(t,e),E(e,n),E(n,i),M(o,i,null),E(i,l),M(a,i,null),E(e,r),E(e,c),C[d].m(c,null),p=!0},p(F,[N]){const b={};4194304&N&&(b.$$scope={dirty:N,ctx:F}),o.$set(b);const $={};1&N&&($.changedFiles=F[0]),32&N&&($.isLoading=F[5]),a.$set($);let y=d;d=v(F),d===y?C[d].p(F,N):(G(),m(C[y],1,1,()=>{C[y]=null}),Y(),f=C[d],f?f.p(F,N):(f=C[d]=k[d](F),f.c()),u(f,1),f.m(c,null))},i(F){p||(u(o.$$.fragment,F),u(a.$$.fragment,F),u(f),p=!0)},o(F){m(o.$$.fragment,F),m(a.$$.fragment,F),m(f),p=!1},d(F){F&&g(t),L(o),L(a),C[d].d()}}}function bs(s,t,e){let n,i,o,l,a,{changedFiles:r}=t,{onApplyChanges:c}=t,{onOpenFile:d}=t,{pendingFiles:f=[]}=t,{appliedFiles:p=[]}=t,{isLoadingTreeView:k=!1}=t,C={},v=!1,F=!1;function N(b,$){e(6,C[b]=$,C)}return s.$$set=b=>{"changedFiles"in b&&e(0,r=b.changedFiles),"onApplyChanges"in b&&e(1,c=b.onApplyChanges),"onOpenFile"in b&&e(2,d=b.onOpenFile),"pendingFiles"in b&&e(3,f=b.pendingFiles),"appliedFiles"in b&&e(4,p=b.appliedFiles),"isLoadingTreeView"in b&&e(5,k=b.isLoadingTreeView)},s.$$.update=()=>{if(1&s.$$.dirty&&e(15,n=JSON.stringify(r)),16&s.$$.dirty&&e(13,i=JSON.stringify(p)),8&s.$$.dirty&&e(14,o=JSON.stringify(f)),32768&s.$$.dirty&&n&&(e(6,C={}),e(7,v=!1),e(8,F=!1)),65&s.$$.dirty&&e(9,a=r.map(b=>{const $=b.new_path||b.old_path,y=b.old_contents||"",_=b.new_contents||"",A=Tn.generateDiff(b.old_path,b.new_path,y,_),q=function(V,P){const H=se("oldFile","newFile",V,P,"","",{context:3}),Z=qn(H);let Q=0,it=0,et=[];for(const nt of Z)for(const X of nt.hunks)for(const ot of X.lines){const pt=ot.startsWith("+"),rt=ot.startsWith("-");pt&&Q++,rt&&it++,et.push({value:ot,added:pt,removed:rt})}return{totalAddedLines:Q,totalRemovedLines:it,changes:et,diff:H}}(y,_);return C[$]||e(6,C[$]=_,C),{qualifiedPathName:{rootPath:"",relPath:$},lineChanges:q,oldContents:y,newContents:_,diff:A}})),57880&s.$$.dirty&&e(10,l=(()=>{if(n&&i&&o){const b=a.map($=>$.qualifiedPathName.relPath);return b.length!==0&&b.some($=>!p.includes($)&&!f.includes($))}return!1})()),664&s.$$.dirty&&v){const b=a.map($=>$.qualifiedPathName.relPath);b.filter($=>!p.includes($)&&!f.includes($)).length===0&&b.every($=>p.includes($)||f.includes($))&&f.length===0&&p.length>0&&(e(7,v=!1),e(8,F=!0))}if(9104&s.$$.dirty&&a.length>0&&!v&&i){const b=a.map($=>$.qualifiedPathName.relPath);if(b.length>0){const $=b.every(y=>p.includes(y));$&&p.length>0?e(8,F=!0):!$&&F&&e(8,F=!1)}}},[r,c,d,f,p,k,C,v,F,a,l,N,function(){if(!c)return;const b=a.map(y=>y.qualifiedPathName.relPath);if(b.every(y=>p.includes(y)))return void e(8,F=!0);const $=b.filter(y=>!p.includes(y)&&!f.includes(y));$.length!==0&&(e(7,v=!0),e(8,F=!1),$.forEach(y=>{const _=a.find(A=>A.qualifiedPathName.relPath===y);if(_){const A=C[y]||_.newContents;c(y,_.oldContents,A)}}))},i,o,n,(b,$)=>{N(b.qualifiedPathName.relPath,$)},b=>{const $=C[b.qualifiedPathName.relPath]||b.newContents;c(b.qualifiedPathName.relPath,b.oldContents,$)},b=>d(b.qualifiedPathName.relPath)]}class Fs extends vt{constructor(t){super(),yt(this,t,bs,xs,ht,{changedFiles:0,onApplyChanges:1,onOpenFile:2,pendingFiles:3,appliedFiles:4,isLoadingTreeView:5})}}function Re(s,t,e){const n=s.slice();return n[3]=t[e],n}function Te(s){let t,e=dt(s[1].paths),n=[];for(let i=0;i<e.length;i+=1)n[i]=Ve(Re(s,e,i));return{c(){for(let i=0;i<n.length;i+=1)n[i].c();t=xt()},m(i,o){for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(i,o);h(i,t,o)},p(i,o){if(2&o){let l;for(e=dt(i[1].paths),l=0;l<e.length;l+=1){const a=Re(i,e,l);n[l]?n[l].p(a,o):(n[l]=Ve(a),n[l].c(),n[l].m(t.parentNode,t))}for(;l<n.length;l+=1)n[l].d(1);n.length=e.length}},d(i){i&&g(t),At(n,i)}}}function Ve(s){let t,e;return{c(){t=dn("path"),w(t,"d",e=s[3]),w(t,"fill-rule","evenodd"),w(t,"clip-rule","evenodd")},m(n,i){h(n,t,i)},p(n,i){2&i&&e!==(e=n[3])&&w(t,"d",e)},d(n){n&&g(t)}}}function As(s){let t,e=s[1]&&Te(s);return{c(){t=dn("svg"),e&&e.c(),w(t,"width","14"),w(t,"viewBox","0 0 20 20"),w(t,"fill","currentColor"),w(t,"class","svelte-10h4f31")},m(n,i){h(n,t,i),e&&e.m(t,null)},p(n,i){n[1]?e?e.p(n,i):(e=Te(n),e.c(),e.m(t,null)):e&&(e.d(1),e=null)},d(n){n&&g(t),e&&e.d()}}}function ks(s){let t,e;return t=new Et({props:{content:`This is a ${s[0]} change`,triggerOn:[Yt.Hover],$$slots:{default:[As]},$$scope:{ctx:s}}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,[i]){const o={};1&i&&(o.content=`This is a ${n[0]} change`),66&i&&(o.$$scope={dirty:i,ctx:n}),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function zs(s,t,e){let n,{type:i}=t;const o={fix:{paths:["M6.56 1.14a.75.75 0 0 1 .177 1.045 3.989 3.989 0 0 0-.464.86c.185.17.382.329.59.473A3.993 3.993 0 0 1 10 2c1.272 0 2.405.594 3.137 1.518.208-.144.405-.302.59-.473a3.989 3.989 0 0 0-.464-.86.75.75 0 0 1 1.222-.869c.369.519.65 1.105.822 1.736a.75.75 0 0 1-.174.707 7.03 7.03 0 0 1-1.299 1.098A4 4 0 0 1 14 6c0 .52-.301.963-.723 1.187a6.961 6.961 0 0 1-1.158.486c.13.208.231.436.296.679 1.413-.174 2.779-.5 4.081-.96a19.655 19.655 0 0 0-.09-2.319.75.75 0 1 1 1.493-.146 21.239 21.239 0 0 1 .08 3.028.75.75 0 0 1-.482.667 20.873 20.873 0 0 1-5.153 1.249 2.521 2.521 0 0 1-.107.247 20.945 20.945 0 0 1 5.252 1.257.75.75 0 0 1 .482.74 20.945 20.945 0 0 1-.908 5.107.75.75 0 0 1-1.433-.444c.415-1.34.69-2.743.806-4.191-.495-.173-1-.327-1.512-.46.05.284.076.575.076.873 0 1.814-.517 3.312-1.426 4.37A4.639 4.639 0 0 1 10 19a4.639 4.639 0 0 1-3.574-1.63C5.516 16.311 5 14.813 5 13c0-.298.026-.59.076-.873-.513.133-1.017.287-1.512.46.116 1.448.39 2.85.806 4.191a.75.75 0 1 1-1.433.444 20.94 20.94 0 0 1-.908-5.107.75.75 0 0 1 .482-.74 20.838 20.838 0 0 1 5.252-1.257 2.493 2.493 0 0 1-.107-.247 20.874 20.874 0 0 1-5.153-1.249.75.75 0 0 1-.482-.667 21.342 21.342 0 0 1 .08-3.028.75.75 0 1 1 1.493.146 19.745 19.745 0 0 0-.09 2.319c1.302.46 2.668.786 4.08.96.066-.243.166-.471.297-.679a6.962 6.962 0 0 1-1.158-.486A1.348 1.348 0 0 1 6 6a4 4 0 0 1 .166-1.143 7.032 7.032 0 0 1-1.3-1.098.75.75 0 0 1-.173-.707 5.48 5.48 0 0 1 .822-1.736.75.75 0 0 1 1.046-.177Z"],color:"var(--ds-color-warning-9)"},feature:{paths:["M14 6a2.5 2.5 0 0 0-4-3 2.5 2.5 0 0 0-4 3H3.25C2.56 6 2 6.56 2 7.25v.5C2 8.44 2.56 9 3.25 9h6V6h1.5v3h6C17.44 9 18 8.44 18 7.75v-.5C18 6.56 17.44 6 16.75 6H14Zm-1-1.5a1 1 0 0 1-1 1h-1v-1a1 1 0 1 1 2 0Zm-6 0a1 1 0 0 0 1 1h1v-1a1 1 0 0 0-2 0Z","M9.25 10.5H3v4.75A2.75 2.75 0 0 0 5.75 18h3.5v-7.5ZM10.75 18v-7.5H17v4.75A2.75 2.75 0 0 1 14.25 18h-3.5Z"],color:"var(--ds-color-warning-9)"},refactor:{paths:["M8.157 2.176a1.5 1.5 0 0 0-1.147 0l-4.084 1.69A1.5 1.5 0 0 0 2 5.25v10.877a1.5 1.5 0 0 0 2.074 1.386l3.51-1.452 4.26 1.762a1.5 1.5 0 0 0 1.146 0l4.083-1.69A1.5 1.5 0 0 0 18 14.75V3.872a1.5 1.5 0 0 0-2.073-1.386l-3.51 1.452-4.26-1.762ZM7.58 5a.75.75 0 0 1 .75.75v6.5a.75.75 0 0 1-1.5 0v-6.5A.75.75 0 0 1 7.58 5Zm5.59 2.75a.75.75 0 0 0-1.5 0v6.5a.75.75 0 0 0 1.5 0v-6.5Z"],color:"var(--ds-color-warning-9)"},documentation:{paths:["M4.5 2A1.5 1.5 0 0 0 3 3.5v13A1.5 1.5 0 0 0 4.5 18h11a1.5 1.5 0 0 0 1.5-1.5V7.621a1.5 1.5 0 0 0-.44-1.06l-4.12-4.122A1.5 1.5 0 0 0 11.378 2H4.5Zm2.25 8.5a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Zm0 3a.75.75 0 0 0 0 1.5h6.5a.75.75 0 0 0 0-1.5h-6.5Z"],color:"var(--ds-color-warning-9)"},style:{paths:["M15.993 1.385a1.87 1.87 0 0 1 2.623 2.622l-4.03 5.27a12.749 12.749 0 0 1-4.237 3.562 4.508 4.508 0 0 0-3.188-3.188 12.75 12.75 0 0 1 3.562-4.236l5.27-4.03ZM6 11a3 3 0 0 0-3 3 .5.5 0 0 1-.72.45.75.75 0 0 0-1.035.931A4.001 4.001 0 0 0 9 14.004V14a3.01 3.01 0 0 0-1.66-2.685A2.99 2.99 0 0 0 6 11Z"],color:"var(--ds-color-warning-9)"},test:{paths:["M8.5 3.528v4.644c0 .729-.29 1.428-.805 1.944l-1.217 1.216a8.75 8.75 0 0 1 3.55.621l.502.201a7.25 7.25 0 0 0 4.178.365l-2.403-2.403a2.75 2.75 0 0 1-.805-1.944V3.528a40.205 40.205 0 0 0-3 0Zm4.5.084.19.015a.75.75 0 1 0 .12-1.495 41.364 41.364 0 0 0-6.62 0 .75.75 0 0 0 .12 1.495L7 3.612v4.56c0 .331-.132.649-.366.883L2.6 13.09c-1.496 1.496-.817 4.15 1.403 4.475C5.961 17.852 7.963 18 10 18s4.039-.148 5.997-.436c2.22-.325 2.9-2.979 1.403-4.475l-4.034-4.034A1.25 1.25 0 0 1 13 8.172v-4.56Z"],color:"var(--ds-color-warning-9)"},chore:{paths:["m6.75.98-.884.883a1.25 1.25 0 1 0 1.768 0L6.75.98ZM13.25.98l-.884.883a1.25 1.25 0 1 0 1.768 0L13.25.98ZM10 .98l.884.883a1.25 1.25 0 1 1-1.768 0L10 .98ZM7.5 5.75a.75.75 0 0 0-1.5 0v.464c-1.179.304-2 1.39-2 2.622v.094c.1-.02.202-.038.306-.052A42.867 42.867 0 0 1 10 8.5c1.93 0 3.83.129 5.694.378.104.014.206.032.306.052v-.094c0-1.232-.821-2.317-2-2.622V5.75a.75.75 0 0 0-1.5 0v.318a45.645 45.645 0 0 0-1.75-.062V5.75a.75.75 0 0 0-1.5 0v.256c-.586.01-1.17.03-1.75.062V5.75ZM4.505 10.365A41.36 41.36 0 0 1 10 10c1.863 0 3.697.124 5.495.365C16.967 10.562 18 11.838 18 13.28v.693a3.72 3.72 0 0 1-1.665-.393 5.222 5.222 0 0 0-4.67 0 3.722 3.722 0 0 1-3.33 0 5.222 5.222 0 0 0-4.67 0A3.72 3.72 0 0 1 2 13.972v-.693c0-1.441 1.033-2.717 2.505-2.914ZM15.665 14.92a5.22 5.22 0 0 0 2.335.552V16.5a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 2 16.5v-1.028c.8 0 1.6-.184 2.335-.551a3.722 3.722 0 0 1 3.33 0c1.47.735 3.2.735 4.67 0a3.722 3.722 0 0 1 3.33 0Z"],color:"var(--ds-color-warning-9)"},performance:{paths:["M4.606 12.97a.75.75 0 0 1-.134 1.051 2.494 2.494 0 0 0-.93 2.437 2.494 2.494 0 0 0 2.437-.93.75.75 0 1 1 1.186.918 3.995 3.995 0 0 1-4.482 1.332.75.75 0 0 1-.461-.461 3.994 3.994 0 0 1 1.332-4.482.75.75 0 0 1 1.052.134Z","M5.752 12A13.07 13.07 0 0 0 8 14.248v4.002c0 .414.336.75.75.75a5 5 0 0 0 4.797-6.414 12.984 12.984 0 0 0 5.45-10.848.75.75 0 0 0-.735-.735 12.984 12.984 0 0 0-10.849 5.45A5 5 0 0 0 1 11.25c.001.414.337.75.751.75h4.002ZM13 9a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"],color:"var(--ds-color-warning-9)"},revert:{paths:["M7.50043 1.37598C7.2023 1.37598 6.91637 1.49431 6.70543 1.70499L6.70521 1.70521L1.70521 6.70521L1.70499 6.70543C1.49431 6.91637 1.37598 7.2023 1.37598 7.50043C1.37598 7.79855 1.49431 8.08449 1.70499 8.29543L1.70521 8.29565L6.69987 13.2903C6.80149 13.3974 6.92322 13.4835 7.05815 13.5436C7.19615 13.6051 7.34512 13.6382 7.49617 13.6408C7.64723 13.6435 7.79727 13.6157 7.93735 13.5591C8.07744 13.5026 8.20469 13.4183 8.31151 13.3115C8.41834 13.2047 8.50256 13.0774 8.55914 12.9374C8.61572 12.7973 8.64351 12.6472 8.64084 12.4962C8.63818 12.3451 8.60511 12.1961 8.54363 12.0581C8.48351 11.9232 8.39743 11.8015 8.29032 11.6999L5.21587 8.62543H12.5004C13.0093 8.62543 13.5132 8.72566 13.9833 8.92039C14.4535 9.11513 14.8806 9.40056 15.2405 9.76039C15.6003 10.1202 15.8857 10.5474 16.0805 11.0175C16.2752 11.4877 16.3754 11.9916 16.3754 12.5004C16.3754 13.0093 16.2752 13.5132 16.0805 13.9833C15.8857 14.4535 15.6003 14.8806 15.2405 15.2405C14.8806 15.6003 14.4535 15.8857 13.9833 16.0805C13.5132 16.2752 13.0093 16.3754 12.5004 16.3754H10.0004C9.70206 16.3754 9.41591 16.494 9.20493 16.7049C8.99395 16.9159 8.87543 17.2021 8.87543 17.5004C8.87543 17.7988 8.99395 18.0849 9.20493 18.2959C9.41591 18.5069 9.70206 18.6254 10.0004 18.6254H12.5004C14.1249 18.6254 15.6828 17.9801 16.8315 16.8315C17.9801 15.6828 18.6254 14.1249 18.6254 12.5004C18.6254 10.876 17.9801 9.31806 16.8315 8.1694C15.6828 7.02074 14.1249 6.37543 12.5004 6.37543H5.21587L8.29565 3.29565L8.29587 3.29543C8.50654 3.08449 8.62488 2.79855 8.62488 2.50043C8.62488 2.2023 8.50654 1.91636 8.29587 1.70543L8.29543 1.70499C8.08449 1.49431 7.79855 1.37598 7.50043 1.37598Z","M7.712 4.818A1.5 1.5 0 0 1 10 6.095v2.972c.104-.13.234-.248.389-.343l6.323-3.906A1.5 1.5 0 0 1 19 6.095v7.81a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.505 1.505 0 0 1-.389-.344v2.973a1.5 1.5 0 0 1-2.288 1.276l-6.323-3.905a1.5 1.5 0 0 1 0-2.552l6.323-3.906Z"],color:"var(--ds-color-warning-9)"},other:{paths:["M2 4.25C2 3.65326 2.23705 3.08097 2.65901 2.65901C3.08097 2.23705 3.65326 2 4.25 2H6.75C7.34674 2 7.91903 2.23705 8.34099 2.65901C8.76295 3.08097 9 3.65326 9 4.25V6.75C9 7.34674 8.76295 7.91903 8.34099 8.34099C7.91903 8.76295 7.34674 9 6.75 9H4.25C3.65326 9 3.08097 8.76295 2.65901 8.34099C2.23705 7.91903 2 7.34674 2 6.75V4.25ZM15.25 11.75C15.25 11.5511 15.171 11.3603 15.0303 11.2197C14.8897 11.079 14.6989 11 14.5 11C14.3011 11 14.1103 11.079 13.9697 11.2197C13.829 11.3603 13.75 11.5511 13.75 11.75V13.75H11.75C11.5511 13.75 11.3603 13.829 11.2197 13.9697C11.079 14.1103 11 14.3011 11 14.5C11 14.6989 11.079 14.8897 11.2197 15.0303C11.3603 15.171 11.5511 15.25 11.75 15.25H13.75V17.25C13.75 17.4489 13.829 17.6397 13.9697 17.7803C14.1103 17.921 14.3011 18 14.5 18C14.6989 18 14.8897 17.921 15.0303 17.7803C15.171 17.6397 15.25 17.4489 15.25 17.25V15.25H17.25C17.4489 15.25 17.6397 15.171 17.7803 15.0303C17.921 14.8897 18 14.6989 18 14.5C18 14.3011 17.921 14.1103 17.7803 13.9697C17.6397 13.829 17.4489 13.75 17.25 13.75H15.25V11.75Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M9 14.5C9 16.433 7.433 18 5.5 18C3.567 18 2 16.433 2 14.5C2 12.567 3.567 11 5.5 11C7.433 11 9 12.567 9 14.5Z","M13.8399 2.86538C14.1332 2.37829 14.867 2.37829 15.1603 2.86538L17.8969 7.40443C18.1901 7.89152 17.8228 8.50006 17.2363 8.50006H11.7635C11.1766 8.50006 10.8097 7.89152 11.1034 7.40443L13.8399 2.86538Z","M 9 14.5 A 3.5 3.5 0 1 1 2 14.5 A 3.5 3.5 0 1 1 9 14.5 Z"],color:"var(--ds-color-warning-9)"}};return s.$$set=l=>{"type"in l&&e(0,i=l.type)},s.$$.update=()=>{1&s.$$.dirty&&e(1,n=o[i]??o.other)},[i,n]}class Ls extends vt{constructor(t){super(),yt(this,t,zs,ks,ht,{type:0})}}function Se(s,t,e){const n=s.slice();return n[1]=t[e],n[3]=e,n}function Ms(s,t,e){const n=s.slice();return n[1]=t[e],n}function Ds(s,t,e){const n=s.slice();return n[1]=t[e],n}function Ns(s){let t;return{c(){t=z("div"),t.innerHTML='<div class="c-skeleton-diff__file-header svelte-1eiztmz"><div class="c-skeleton-diff__file-info svelte-1eiztmz"><div class="c-skeleton-diff__file-icon svelte-1eiztmz"></div> <div class="c-skeleton-diff__file-path svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__file-actions svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__code-block svelte-1eiztmz"><div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 70%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 85%;"></span></div> <div class="c-skeleton-diff__code-line svelte-1eiztmz"><span class="c-skeleton-diff__line-number svelte-1eiztmz"></span> <span class="c-skeleton-diff__line-content svelte-1eiztmz" style="width: 60%;"></span></div></div>',w(t,"class","c-skeleton-diff__changes-item svelte-1eiztmz")},m(e,n){h(e,t,n)},p:at,d(e){e&&g(t)}}}function qs(s){let t,e,n,i,o=dt(Array(2)),l=[];for(let a=0;a<o.length;a+=1)l[a]=Ns(Ds(s,o,a));return{c(){t=z("div"),e=z("div"),e.innerHTML='<div class="c-skeleton-diff__content svelte-1eiztmz"><div class="c-skeleton-diff__subtitle svelte-1eiztmz"></div></div> <div class="c-skeleton-diff__icon svelte-1eiztmz"></div>',n=S(),i=z("div");for(let a=0;a<l.length;a+=1)l[a].c();w(e,"class","c-skeleton-diff__header svelte-1eiztmz"),w(i,"class","c-skeleton-diff__changes svelte-1eiztmz"),w(t,"class","c-skeleton-diff__subsection svelte-1eiztmz")},m(a,r){h(a,t,r),E(t,e),E(t,n),E(t,i);for(let c=0;c<l.length;c+=1)l[c]&&l[c].m(i,null)},p:at,d(a){a&&g(t),At(l,a)}}}function je(s){let t,e,n,i,o,l,a=s[3]===0&&function(d){let f;return{c(){f=z("div"),f.innerHTML='<div class="c-skeleton-diff__button svelte-1eiztmz"></div>',w(f,"class","c-skeleton-diff__controls svelte-1eiztmz")},m(p,k){h(p,f,k)},d(p){p&&g(f)}}}(),r=dt(Array(2)),c=[];for(let d=0;d<r.length;d+=1)c[d]=qs(Ms(s,r,d));return{c(){t=z("div"),e=z("div"),n=z("div"),n.innerHTML='<div class="c-skeleton-diff__title svelte-1eiztmz"></div> <div class="c-skeleton-diff__description svelte-1eiztmz"><div class="c-skeleton-diff__line svelte-1eiztmz"></div> <div class="c-skeleton-diff__line svelte-1eiztmz" style="width: 85%;"></div></div>',i=S(),a&&a.c(),o=S();for(let d=0;d<c.length;d+=1)c[d].c();l=S(),w(n,"class","c-skeleton-diff__content svelte-1eiztmz"),w(e,"class","c-skeleton-diff__header svelte-1eiztmz"),w(t,"class","c-skeleton-diff__section svelte-1eiztmz")},m(d,f){h(d,t,f),E(t,e),E(e,n),E(e,i),a&&a.m(e,null),E(t,o);for(let p=0;p<c.length;p+=1)c[p]&&c[p].m(t,null);E(t,l)},p(d,f){},d(d){d&&g(t),a&&a.d(),At(c,d)}}}function Es(s){let t,e=dt(Array(s[0])),n=[];for(let i=0;i<e.length;i+=1)n[i]=je(Se(s,e,i));return{c(){t=z("div");for(let i=0;i<n.length;i+=1)n[i].c();w(t,"class","c-skeleton-diff svelte-1eiztmz")},m(i,o){h(i,t,o);for(let l=0;l<n.length;l+=1)n[l]&&n[l].m(t,null)},p(i,[o]){if(1&o){let l;for(e=dt(Array(i[0])),l=0;l<e.length;l+=1){const a=Se(i,e,l);n[l]?n[l].p(a,o):(n[l]=je(a),n[l].c(),n[l].m(t,null))}for(;l<n.length;l+=1)n[l].d(1);n.length=e.length}},i:at,o:at,d(i){i&&g(t),At(n,i)}}}function Os(s,t,e){let{count:n=2}=t;return s.$$set=i=>{"count"in i&&e(0,n=i.count)},[n]}class Ps extends vt{constructor(t){super(),yt(this,t,Os,Es,ht,{count:0})}}function He(s,t,e){const n=s.slice();return n[4]=t[e],n}function Rs(s){let t;return{c(){t=R(`The following files will have merge conflicts if applied locally. Conflict markers will be
        added to the file which can be resolved manually after applying.`)},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function Ts(s){let t;return{c(){t=R("The following files have merge conflicts that need to be resolved manually.")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function Vs(s){let t,e,n,i;return t=new gn({props:{filename:s[4]}}),n=new ii({props:{filepath:s[4]}}),{c(){D(t.$$.fragment),e=S(),D(n.$$.fragment)},m(o,l){M(t,o,l),h(o,e,l),M(n,o,l),i=!0},p(o,l){const a={};1&l&&(a.filename=o[4]),t.$set(a);const r={};1&l&&(r.filepath=o[4]),n.$set(r)},i(o){i||(u(t.$$.fragment,o),u(n.$$.fragment,o),i=!0)},o(o){m(t.$$.fragment,o),m(n.$$.fragment,o),i=!1},d(o){o&&g(e),L(t,o),L(n,o)}}}function Ss(s){let t,e;return t=new $n({}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function js(s){let t,e;return t=new ae({props:{size:1,variant:"ghost-block",color:"neutral",$$slots:{default:[Ss]},$$scope:{ctx:s}}}),t.$on("click",function(){return s[3](s[4])}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){s=n;const o={};128&i&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function Be(s){let t,e,n,i,o,l;return e=new Et({props:{content:s[4],nested:!0,$$slots:{default:[Vs]},$$scope:{ctx:s}}}),i=new Et({props:{content:"Open file",$$slots:{default:[js]},$$scope:{ctx:s}}}),{c(){t=z("div"),D(e.$$.fragment),n=S(),D(i.$$.fragment),o=S(),w(t,"class","c-conflicts-card__file svelte-1bce35u")},m(a,r){h(a,t,r),M(e,t,null),E(t,n),M(i,t,null),E(t,o),l=!0},p(a,r){const c={};1&r&&(c.content=a[4]),129&r&&(c.$$scope={dirty:r,ctx:a}),e.$set(c);const d={};133&r&&(d.$$scope={dirty:r,ctx:a}),i.$set(d)},i(a){l||(u(e.$$.fragment,a),u(i.$$.fragment,a),l=!0)},o(a){m(e.$$.fragment,a),m(i.$$.fragment,a),l=!1},d(a){a&&g(t),L(e),L(i)}}}function Hs(s){let t,e,n,i,o,l,a,r,c,d,f,p,k,C=s[0].size+"";function v(_,A){return _[1]?Ts:Rs}n=new mn({});let F=v(s),N=F(s),b=dt(s[0]),$=[];for(let _=0;_<b.length;_+=1)$[_]=Be(He(s,b,_));const y=_=>m($[_],1,1,()=>{$[_]=null});return{c(){t=z("div"),e=z("div"),D(n.$$.fragment),i=S(),o=z("span"),l=R("Conflicts ("),a=R(C),r=R(")"),c=S(),d=z("div"),N.c(),f=S();for(let _=0;_<$.length;_+=1)$[_].c();p=xt(),w(e,"class","c-conflicts-card__icon svelte-1bce35u"),w(o,"class","c-conflicts-card__title svelte-1bce35u"),w(t,"class","c-conflicts-card__header svelte-1bce35u"),w(d,"class","c-conflicts-card__description svelte-1bce35u")},m(_,A){h(_,t,A),E(t,e),M(n,e,null),E(t,i),E(t,o),E(o,l),E(o,a),E(o,r),h(_,c,A),h(_,d,A),N.m(d,null),h(_,f,A);for(let q=0;q<$.length;q+=1)$[q]&&$[q].m(_,A);h(_,p,A),k=!0},p(_,A){if((!k||1&A)&&C!==(C=_[0].size+"")&&ct(a,C),F!==(F=v(_))&&(N.d(1),N=F(_),N&&(N.c(),N.m(d,null))),5&A){let q;for(b=dt(_[0]),q=0;q<b.length;q+=1){const V=He(_,b,q);$[q]?($[q].p(V,A),u($[q],1)):($[q]=Be(V),$[q].c(),u($[q],1),$[q].m(p.parentNode,p))}for(G(),q=b.length;q<$.length;q+=1)y(q);Y()}},i(_){if(!k){u(n.$$.fragment,_);for(let A=0;A<b.length;A+=1)u($[A]);k=!0}},o(_){m(n.$$.fragment,_),$=$.filter(Boolean);for(let A=0;A<$.length;A+=1)m($[A]);k=!1},d(_){_&&(g(t),g(c),g(d),g(f),g(p)),L(n),N.d(),At($,_)}}}function Bs(s){let t,e,n;return e=new Gn({props:{includeBackground:!1,$$slots:{default:[Hs]},$$scope:{ctx:s}}}),{c(){t=z("div"),D(e.$$.fragment),w(t,"class","c-conflicts-card")},m(i,o){h(i,t,o),M(e,t,null),n=!0},p(i,[o]){const l={};135&o&&(l.$$scope={dirty:o,ctx:i}),e.$set(l)},i(i){n||(u(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){i&&g(t),L(e)}}}function Us(s,t,e){let{files:n}=t,{hasAppliedAll:i}=t,{onOpenFile:o}=t;return s.$$set=l=>{"files"in l&&e(0,n=l.files),"hasAppliedAll"in l&&e(1,i=l.hasAppliedAll),"onOpenFile"in l&&e(2,o=l.onOpenFile)},[n,i,o,l=>o==null?void 0:o(l)]}class Zs extends vt{constructor(t){super(),yt(this,t,Us,Bs,ht,{files:0,hasAppliedAll:1,onOpenFile:2})}}function Is(s){let t,e,n,i;return e=new Vn({props:{token:{type:"codespan",text:"`git stash`",raw:"`git stash`"}}}),{c(){t=R(`There are unstaged changes in your working directory. Please commit your changes or we will
      run
      `),D(e.$$.fragment),n=R(`
      to stash your changes before applying changes from the remote agent.`)},m(o,l){h(o,t,l),M(e,o,l),h(o,n,l),i=!0},p:at,i(o){i||(u(e.$$.fragment,o),i=!0)},o(o){m(e.$$.fragment,o),i=!1},d(o){o&&(g(t),g(n)),L(e,o)}}}function Ue(s){let t,e;return t=new lt({props:{$$slots:{default:[Ws]},$$scope:{ctx:s}}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};130&i&&(o.$$scope={dirty:i,ctx:n}),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function Ws(s){let t;return{c(){t=R(s[1])},m(e,n){h(e,t,n)},p(e,n){2&n&&ct(t,e[1])},d(e){e&&g(t)}}}function Js(s){let t,e,n,i;e=new lt({props:{$$slots:{default:[Is]},$$scope:{ctx:s}}});let o=s[1]&&Ue(s);return{c(){t=z("div"),D(e.$$.fragment),n=S(),o&&o.c(),w(t,"class","c-unstaged-changes-modal__body svelte-9eyy34")},m(l,a){h(l,t,a),M(e,t,null),E(t,n),o&&o.m(t,null),i=!0},p(l,a){const r={};128&a&&(r.$$scope={dirty:a,ctx:l}),e.$set(r),l[1]?o?(o.p(l,a),2&a&&u(o,1)):(o=Ue(l),o.c(),u(o,1),o.m(t,null)):o&&(G(),m(o,1,1,()=>{o=null}),Y())},i(l){i||(u(e.$$.fragment,l),u(o),i=!0)},o(l){m(e.$$.fragment,l),m(o),i=!1},d(l){l&&g(t),L(e),o&&o.d()}}}function Ze(s){let t,e,n;return e=new Xt({props:{size:1}}),{c(){t=z("div"),D(e.$$.fragment),w(t,"class","c-unstaged-changes-modal__stash-button-loading svelte-9eyy34")},m(i,o){h(i,t,o),M(e,t,null),n=!0},i(i){n||(u(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){i&&g(t),L(e)}}}function Gs(s){let t,e,n,i=s[2]&&Ze();return{c(){i&&i.c(),t=S(),e=z("span"),e.textContent="Stash & Apply Locally",w(e,"class","c-unstaged-changes-modal__stash-button-text svelte-9eyy34"),gt(e,"loading",s[2])},m(o,l){i&&i.m(o,l),h(o,t,l),h(o,e,l),n=!0},p(o,l){o[2]?i?4&l&&u(i,1):(i=Ze(),i.c(),u(i,1),i.m(t.parentNode,t)):i&&(G(),m(i,1,1,()=>{i=null}),Y()),(!n||4&l)&&gt(e,"loading",o[2])},i(o){n||(u(i),n=!0)},o(o){m(i),n=!1},d(o){o&&(g(t),g(e)),i&&i.d(o)}}}function Ys(s){let t;return{c(){t=R("Abort")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function Ks(s){let t,e,n,i,o;return e=new Ft({props:{variant:"solid",color:"accent",disabled:!!s[1]||s[2],$$slots:{default:[Gs]},$$scope:{ctx:s}}}),e.$on("click",s[3]),i=new Ft({props:{variant:"solid",color:"neutral",disabled:s[2],$$slots:{default:[Ys]},$$scope:{ctx:s}}}),i.$on("click",s[4]),{c(){t=z("div"),D(e.$$.fragment),n=S(),D(i.$$.fragment),w(t,"class","c-unstaged-changes-modal__footer svelte-9eyy34"),w(t,"slot","footer")},m(l,a){h(l,t,a),M(e,t,null),E(t,n),M(i,t,null),o=!0},p(l,a){const r={};6&a&&(r.disabled=!!l[1]||l[2]),132&a&&(r.$$scope={dirty:a,ctx:l}),e.$set(r);const c={};4&a&&(c.disabled=l[2]),128&a&&(c.$$scope={dirty:a,ctx:l}),i.$set(c)},i(l){o||(u(e.$$.fragment,l),u(i.$$.fragment,l),o=!0)},o(l){m(e.$$.fragment,l),m(i.$$.fragment,l),o=!1},d(l){l&&g(t),L(e),L(i)}}}function Qs(s){let t,e;return t=new si({props:{show:s[0],title:"Unstaged changes",$$slots:{footer:[Ks],default:[Js]},$$scope:{ctx:s}}}),t.$on("cancel",s[4]),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,[i]){const o={};1&i&&(o.show=n[0]),134&i&&(o.$$scope={dirty:i,ctx:n}),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function Xs(s,t,e){let{showModal:n=!1}=t,{applyAllChanges:i}=t;const o=Rt(Vt.key);let l,a=!1;return s.$$set=r=>{"showModal"in r&&e(0,n=r.showModal),"applyAllChanges"in r&&e(5,i=r.applyAllChanges)},[n,l,a,async function(){if(e(2,a=!0),!await o.stashUnstagedChanges())return e(1,l="Failed to stash changes. Please manually stash or commit your unstaged changes."),void e(2,a=!1);await new Promise(r=>setTimeout(r,1500)),e(1,l=void 0),e(0,n=!1),i(),e(2,a=!1)},function(){e(0,n=!1),e(1,l=void 0)},i]}class to extends vt{constructor(t){super(),yt(this,t,Xs,Qs,ht,{showModal:0,applyAllChanges:5})}}const{Boolean:xn,Map:eo}=Dn;function Ie(s,t,e){const n=s.slice();return n[55]=t[e],n[56]=t,n[57]=e,n}function We(s,t,e){const n=s.slice();return n[58]=t[e],n[59]=t,n[60]=e,n}function Je(s,t,e){const n=s.slice();return n[61]=t[e],n[62]=t,n[63]=e,n}function ne(s){const t=s.slice(),e=t[11]?t[6]:t[23];return t[64]=e,t}function Ge(s){let t,e,n,i,o,l,a,r;e=new hn({}),l=new Ft({props:{variant:"ghost",size:1,$$slots:{default:[no]},$$scope:{ctx:s}}}),l.$on("click",s[34]);let c=s[4]&&Ye(s);return{c(){t=z("div"),D(e.$$.fragment),n=S(),i=R(s[20]),o=S(),D(l.$$.fragment),a=S(),c&&c.c(),w(t,"class","c-diff-view__error svelte-ibi4q5")},m(d,f){h(d,t,f),M(e,t,null),E(t,n),E(t,i),E(t,o),M(l,t,null),E(t,a),c&&c.m(t,null),r=!0},p(d,f){(!r||1048576&f[0])&&ct(i,d[20]);const p={};8&f[2]&&(p.$$scope={dirty:f,ctx:d}),l.$set(p),d[4]?c?(c.p(d,f),16&f[0]&&u(c,1)):(c=Ye(d),c.c(),u(c,1),c.m(t,null)):c&&(G(),m(c,1,1,()=>{c=null}),Y())},i(d){r||(u(e.$$.fragment,d),u(l.$$.fragment,d),u(c),r=!0)},o(d){m(e.$$.fragment,d),m(l.$$.fragment,d),m(c),r=!1},d(d){d&&g(t),L(e),L(l),c&&c.d()}}}function no(s){let t;return{c(){t=R("Retry")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function Ye(s){let t,e;return t=new Ft({props:{variant:"ghost",size:1,$$slots:{default:[io]},$$scope:{ctx:s}}}),t.$on("click",function(){cn(s[4])&&s[4].apply(this,arguments)}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){s=n;const o={};8&i[2]&&(o.$$scope={dirty:i,ctx:s}),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function io(s){let t;return{c(){t=R("Render as list")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function so(s){let t,e,n,i,o,l,a,r,c,d,f,p,k,C=s[1]&&s[2]!==s[1]&&Ke(s),v=s[2]&&Qe(s);l=new lt({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[fo]},$$scope:{ctx:s}}}),r=new Cn({props:{changedFiles:s[0]}});const F=[uo,po],N=[];function b($,y){return $[18]&&$[17].length===0?0:$[7]&&$[7].length>0?1:-1}return~(f=b(s))&&(p=N[f]=F[f](s)),{c(){t=z("div"),e=z("div"),n=z("div"),C&&C.c(),i=S(),v&&v.c(),o=S(),D(l.$$.fragment),a=S(),D(r.$$.fragment),c=S(),d=z("div"),p&&p.c(),w(n,"class","c-diff-view__tree__header svelte-ibi4q5"),w(e,"class","c-diff-view__tree svelte-ibi4q5"),w(d,"class","c-diff-view__explanation svelte-ibi4q5"),w(t,"class","c-diff-view__layout svelte-ibi4q5")},m($,y){h($,t,y),E(t,e),E(e,n),C&&C.m(n,null),E(n,i),v&&v.m(n,null),E(n,o),M(l,n,null),E(n,a),M(r,n,null),E(t,c),E(t,d),~f&&N[f].m(d,null),k=!0},p($,y){$[1]&&$[2]!==$[1]?C?(C.p($,y),6&y[0]&&u(C,1)):(C=Ke($),C.c(),u(C,1),C.m(n,i)):C&&(G(),m(C,1,1,()=>{C=null}),Y()),$[2]?v?(v.p($,y),4&y[0]&&u(v,1)):(v=Qe($),v.c(),u(v,1),v.m(n,o)):v&&(G(),m(v,1,1,()=>{v=null}),Y());const _={};8&y[2]&&(_.$$scope={dirty:y,ctx:$}),l.$set(_);const A={};1&y[0]&&(A.changedFiles=$[0]),r.$set(A);let q=f;f=b($),f===q?~f&&N[f].p($,y):(p&&(G(),m(N[q],1,1,()=>{N[q]=null}),Y()),~f?(p=N[f],p?p.p($,y):(p=N[f]=F[f]($),p.c()),u(p,1),p.m(d,null)):p=null)},i($){k||(u(C),u(v),u(l.$$.fragment,$),u(r.$$.fragment,$),u(p),k=!0)},o($){m(C),m(v),m(l.$$.fragment,$),m(r.$$.fragment,$),m(p),k=!1},d($){$&&g(t),C&&C.d(),v&&v.d(),L(l),L(r),~f&&N[f].d()}}}function oo(s){let t,e,n;return e=new lt({props:{size:2,color:"secondary",$$slots:{default:[Vo]},$$scope:{ctx:s}}}),{c(){t=z("div"),D(e.$$.fragment),w(t,"class","c-diff-view__empty svelte-ibi4q5")},m(i,o){h(i,t,o),M(e,t,null),n=!0},p(i,o){const l={};8&o[2]&&(l.$$scope={dirty:o,ctx:i}),e.$set(l)},i(i){n||(u(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){i&&g(t),L(e)}}}function Ke(s){let t,e,n,i;return t=new lt({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[lo]},$$scope:{ctx:s}}}),n=new lt({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[ro]},$$scope:{ctx:s}}}),{c(){D(t.$$.fragment),e=S(),D(n.$$.fragment)},m(o,l){M(t,o,l),h(o,e,l),M(n,o,l),i=!0},p(o,l){const a={};8&l[2]&&(a.$$scope={dirty:l,ctx:o}),t.$set(a);const r={};2&l[0]|8&l[2]&&(r.$$scope={dirty:l,ctx:o}),n.$set(r)},i(o){i||(u(t.$$.fragment,o),u(n.$$.fragment,o),i=!0)},o(o){m(t.$$.fragment,o),m(n.$$.fragment,o),i=!1},d(o){o&&g(e),L(t,o),L(n,o)}}}function lo(s){let t;return{c(){t=R("Changes from agent")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function ro(s){let t;return{c(){t=R(s[1])},m(e,n){h(e,t,n)},p(e,n){2&n[0]&&ct(t,e[1])},d(e){e&&g(t)}}}function Qe(s){let t,e,n,i;return t=new lt({props:{size:1,class:"c-diff-view__tree__header__label",$$slots:{default:[ao]},$$scope:{ctx:s}}}),n=new lt({props:{size:1,weight:"medium",class:"c-diff-view__tree__header__title",$$slots:{default:[co]},$$scope:{ctx:s}}}),{c(){D(t.$$.fragment),e=S(),D(n.$$.fragment)},m(o,l){M(t,o,l),h(o,e,l),M(n,o,l),i=!0},p(o,l){const a={};8&l[2]&&(a.$$scope={dirty:l,ctx:o}),t.$set(a);const r={};4&l[0]|8&l[2]&&(r.$$scope={dirty:l,ctx:o}),n.$set(r)},i(o){i||(u(t.$$.fragment,o),u(n.$$.fragment,o),i=!0)},o(o){m(t.$$.fragment,o),m(n.$$.fragment,o),i=!1},d(o){o&&g(e),L(t,o),L(n,o)}}}function ao(s){let t;return{c(){t=R("Last user prompt")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function co(s){let t;return{c(){t=R(s[2])},m(e,n){h(e,t,n)},p(e,n){4&n[0]&&ct(t,e[2])},d(e){e&&g(t)}}}function fo(s){let t;return{c(){t=R("Changed files")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function po(s){let t,e,n=dt(s[7]),i=[];for(let l=0;l<n.length;l+=1)i[l]=sn(Ie(s,n,l));const o=l=>m(i[l],1,1,()=>{i[l]=null});return{c(){for(let l=0;l<i.length;l+=1)i[l].c();t=xt()},m(l,a){for(let r=0;r<i.length;r+=1)i[r]&&i[r].m(l,a);h(l,t,a),e=!0},p(l,a){if(1793708008&a[0]|3&a[1]){let r;for(n=dt(l[7]),r=0;r<n.length;r+=1){const c=Ie(l,n,r);i[r]?(i[r].p(c,a),u(i[r],1)):(i[r]=sn(c),i[r].c(),u(i[r],1),i[r].m(t.parentNode,t))}for(G(),r=n.length;r<i.length;r+=1)o(r);Y()}},i(l){if(!e){for(let a=0;a<n.length;a+=1)u(i[a]);e=!0}},o(l){i=i.filter(xn);for(let a=0;a<i.length;a+=1)m(i[a]);e=!1},d(l){l&&g(t),At(i,l)}}}function uo(s){let t,e,n,i,o;return e=new Et({props:{content:s[10]?"Applying changes...":s[11]?"All changes applied":s[12]?"Apply all changes":"No changes to apply",$$slots:{default:[To]},$$scope:{ctx:s}}}),i=new Ps({props:{count:2}}),{c(){t=z("div"),D(e.$$.fragment),n=S(),D(i.$$.fragment),w(t,"class","c-diff-view__controls svelte-ibi4q5")},m(l,a){h(l,t,a),M(e,t,null),h(l,n,a),M(i,l,a),o=!0},p(l,a){const r={};7168&a[0]&&(r.content=l[10]?"Applying changes...":l[11]?"All changes applied":l[12]?"Apply all changes":"No changes to apply"),15360&a[0]|8&a[2]&&(r.$$scope={dirty:a,ctx:l}),e.$set(r)},i(l){o||(u(e.$$.fragment,l),u(i.$$.fragment,l),o=!0)},o(l){m(e.$$.fragment,l),m(i.$$.fragment,l),o=!1},d(l){l&&(g(t),g(n)),L(e),L(i,l)}}}function $o(s){let t,e=s[55].title+"";return{c(){t=R(e)},m(n,i){h(n,t,i)},p(n,i){128&i[0]&&e!==(e=n[55].title+"")&&ct(t,e)},d(n){n&&g(t)}}}function mo(s){let t;return{c(){t=z("div"),w(t,"class","c-diff-view__skeleton-title svelte-ibi4q5")},m(e,n){h(e,t,n)},p:at,d(e){e&&g(t)}}}function go(s){let t,e;return t=new oe({props:{markdown:s[55].description}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};128&i[0]&&(o.markdown=n[55].description),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function ho(s){let t,e,n;return{c(){t=z("div"),e=S(),n=z("div"),w(t,"class","c-diff-view__skeleton-text svelte-ibi4q5"),w(n,"class","c-diff-view__skeleton-text svelte-ibi4q5")},m(i,o){h(i,t,o),h(i,e,o),h(i,n,o)},p:at,i:at,o:at,d(i){i&&(g(t),g(e),g(n))}}}function vo(s){let t,e,n;return t=new Sn({}),{c(){D(t.$$.fragment),e=R(`
                        Expand All`)},m(i,o){M(t,i,o),h(i,e,o),n=!0},i(i){n||(u(t.$$.fragment,i),n=!0)},o(i){m(t.$$.fragment,i),n=!1},d(i){i&&g(e),L(t,i)}}}function yo(s){let t,e,n;return t=new Bn({}),{c(){D(t.$$.fragment),e=R(`
                        Collapse All`)},m(i,o){M(t,i,o),h(i,e,o),n=!0},i(i){n||(u(t.$$.fragment,i),n=!0)},o(i){m(t.$$.fragment,i),n=!1},d(i){i&&g(e),L(t,i)}}}function wo(s){let t,e,n,i;const o=[yo,vo],l=[];function a(r,c){return r[27]?1:0}return t=a(s),e=l[t]=o[t](s),{c(){e.c(),n=xt()},m(r,c){l[t].m(r,c),h(r,n,c),i=!0},p(r,c){let d=t;t=a(r),t!==d&&(G(),m(l[d],1,1,()=>{l[d]=null}),Y(),e=l[t],e||(e=l[t]=o[t](r),e.c()),u(e,1),e.m(n.parentNode,n))},i(r){i||(u(e),i=!0)},o(r){m(e),i=!1},d(r){r&&g(n),l[t].d(r)}}}function _o(s){let t,e,n,i;return n=new te({}),{c(){t=R(`Apply All
                          `),e=z("div"),D(n.$$.fragment),w(e,"class","c-diff-view__controls__icon svelte-ibi4q5")},m(o,l){h(o,t,l),h(o,e,l),M(n,e,null),i=!0},p:at,i(o){i||(u(n.$$.fragment,o),i=!0)},o(o){m(n.$$.fragment,o),i=!1},d(o){o&&(g(t),g(e)),L(n)}}}function Co(s){let t,e,n,i,o,l;e=new lt({props:{size:2,$$slots:{default:[bo]},$$scope:{ctx:s}}});const a=[Ao,Fo],r=[];function c(d,f){return d[14]?0:1}return i=c(s),o=r[i]=a[i](s),{c(){t=z("div"),D(e.$$.fragment),n=S(),o.c(),w(t,"class","c-diff-view__applied svelte-ibi4q5")},m(d,f){h(d,t,f),M(e,t,null),E(t,n),r[i].m(t,null),l=!0},p(d,f){const p={};8&f[2]&&(p.$$scope={dirty:f,ctx:d}),e.$set(p);let k=i;i=c(d),i!==k&&(G(),m(r[k],1,1,()=>{r[k]=null}),Y(),o=r[i],o||(o=r[i]=a[i](d),o.c()),u(o,1),o.m(t,null))},i(d){l||(u(e.$$.fragment,d),u(o),l=!0)},o(d){m(e.$$.fragment,d),m(o),l=!1},d(d){d&&g(t),L(e),r[i].d()}}}function xo(s){let t,e,n,i,o;return e=new Xt({props:{size:1,useCurrentColor:!0}}),i=new lt({props:{size:2,$$slots:{default:[ko]},$$scope:{ctx:s}}}),{c(){t=z("div"),D(e.$$.fragment),n=S(),D(i.$$.fragment),w(t,"class","c-diff-view__applying svelte-ibi4q5")},m(l,a){h(l,t,a),M(e,t,null),E(t,n),M(i,t,null),o=!0},p(l,a){const r={};8&a[2]&&(r.$$scope={dirty:a,ctx:l}),i.$set(r)},i(l){o||(u(e.$$.fragment,l),u(i.$$.fragment,l),o=!0)},o(l){m(e.$$.fragment,l),m(i.$$.fragment,l),o=!1},d(l){l&&g(t),L(e),L(i)}}}function bo(s){let t;return{c(){t=R("Applied")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function Fo(s){let t,e;return t=new de({props:{iconName:"check"}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function Ao(s){let t,e;return t=new mn({props:{slot:"rightIcon"}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function ko(s){let t;return{c(){t=R("Applying...")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function zo(s){let t,e,n,i;const o=[xo,Co,_o],l=[];function a(r,c){return r[10]?0:r[11]?1:2}return t=a(s),e=l[t]=o[t](s),{c(){e.c(),n=xt()},m(r,c){l[t].m(r,c),h(r,n,c),i=!0},p(r,c){let d=t;t=a(r),t===d?l[t].p(r,c):(G(),m(l[d],1,1,()=>{l[d]=null}),Y(),e=l[t],e?e.p(r,c):(e=l[t]=o[t](r),e.c()),u(e,1),e.m(n.parentNode,n))},i(r){i||(u(e),i=!0)},o(r){m(e),i=!1},d(r){r&&g(n),l[t].d(r)}}}function Lo(s){let t,e;return t=new Ft({props:{variant:"ghost-block",color:"neutral",size:2,disabled:s[15],$$slots:{default:[zo]},$$scope:{ctx:s}}}),t.$on("click",s[32]),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};32768&i[0]&&(o.disabled=n[15]),19456&i[0]|8&i[2]&&(o.$$scope={dirty:i,ctx:n}),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function Xe(s){let t,e;return t=new Zs({props:{files:s[64],hasAppliedAll:s[11],onOpenFile:s[3]}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};8390720&i[0]&&(o.files=n[64]),2048&i[0]&&(o.hasAppliedAll=n[11]),8&i[0]&&(o.onOpenFile=n[3]),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function Mo(s){let t,e=s[58].title+"";return{c(){t=R(e)},m(n,i){h(n,t,i)},p(n,i){128&i[0]&&e!==(e=n[58].title+"")&&ct(t,e)},d(n){n&&g(t)}}}function Do(s){let t;return{c(){t=z("div"),w(t,"class","c-diff-view__skeleton-text svelte-ibi4q5")},m(e,n){h(e,t,n)},p:at,d(e){e&&g(t)}}}function tn(s){let t,e,n,i,o,l=s[58].warning+"";return e=new hn({}),{c(){t=z("div"),D(e.$$.fragment),n=S(),i=R(l),w(t,"class","c-diff-view__warning svelte-ibi4q5")},m(a,r){h(a,t,r),M(e,t,null),E(t,n),E(t,i),o=!0},p(a,r){(!o||128&r[0])&&l!==(l=a[58].warning+"")&&ct(i,l)},i(a){o||(u(e.$$.fragment,a),o=!0)},o(a){m(e.$$.fragment,a),o=!1},d(a){a&&g(t),L(e)}}}function en(s,t){let e,n,i,o,l,a=t[57],r=t[60],c=t[61];function d(...b){return t[42](t[61],...b)}function f(){return t[43](t[61])}function p(){return t[44](t[61])}function k(b){t[45](b,t[61])}const C=()=>t[46](n,a,r,c),v=()=>t[46](null,a,r,c);function F(b){t[47](b)}let N={path:t[61].path,change:t[61],descriptions:t[58].descriptions,isExpandedDefault:t[9][t[61].path]!==void 0?!t[9][t[61].path]:t[8],isApplying:t[16][t[61].path]==="pending",hasApplied:t[16][t[61].path]==="applied",onCodeChange:d,onApplyChanges:f,onOpenFile:t[3]?p:void 0,isAgentFromDifferentRepo:t[5]};return t[9][t[61].path]!==void 0&&(N.isCollapsed=t[9][t[61].path]),t[22]!==void 0&&(N.areDescriptionsVisible=t[22]),n=new wn({props:N}),Mt.push(()=>Ht(n,"isCollapsed",k)),C(),Mt.push(()=>Ht(n,"areDescriptionsVisible",F)),{key:s,first:null,c(){e=z("div"),D(n.$$.fragment),w(e,"class","c-diff-view__changes-item svelte-ibi4q5"),this.first=e},m(b,$){h(b,e,$),M(n,e,null),l=!0},p(b,$){a===(t=b)[57]&&r===t[60]&&c===t[61]||(v(),a=t[57],r=t[60],c=t[61],C());const y={};128&$[0]&&(y.path=t[61].path),128&$[0]&&(y.change=t[61]),128&$[0]&&(y.descriptions=t[58].descriptions),896&$[0]&&(y.isExpandedDefault=t[9][t[61].path]!==void 0?!t[9][t[61].path]:t[8]),65664&$[0]&&(y.isApplying=t[16][t[61].path]==="pending"),65664&$[0]&&(y.hasApplied=t[16][t[61].path]==="applied"),128&$[0]&&(y.onCodeChange=d),128&$[0]&&(y.onApplyChanges=f),136&$[0]&&(y.onOpenFile=t[3]?p:void 0),32&$[0]&&(y.isAgentFromDifferentRepo=t[5]),!i&&640&$[0]&&(i=!0,y.isCollapsed=t[9][t[61].path],Bt(()=>i=!1)),!o&&4194304&$[0]&&(o=!0,y.areDescriptionsVisible=t[22],Bt(()=>o=!1)),n.$set(y)},i(b){l||(u(n.$$.fragment,b),l=!0)},o(b){m(n.$$.fragment,b),l=!1},d(b){b&&g(e),v(),L(n)}}}function nn(s){let t,e,n,i,o,l,a,r,c,d,f,p=[],k=new eo;function C(y,_){return y[19]&&y[58].descriptions.length===0?Do:Mo}o=new Ls({props:{type:s[58].type}});let v=C(s),F=v(s),N=!s[19]&&s[58].warning&&tn(s),b=dt(s[58].changes);const $=y=>y[61].id;for(let y=0;y<b.length;y+=1){let _=Je(s,b,y),A=$(_);k.set(A,p[y]=en(A,_))}return{c(){t=z("div"),e=z("div"),n=z("div"),i=z("div"),D(o.$$.fragment),l=S(),a=z("h5"),F.c(),r=S(),N&&N.c(),c=S(),d=z("div");for(let y=0;y<p.length;y+=1)p[y].c();w(i,"class","c-diff-view__icon svelte-ibi4q5"),w(a,"class","c-diff-view__title svelte-ibi4q5"),w(n,"class","c-diff-view__content svelte-ibi4q5"),w(e,"class","c-diff-view__header svelte-ibi4q5"),w(d,"class","c-diff-view__changes svelte-ibi4q5"),w(t,"class","c-diff-view__subsection svelte-ibi4q5"),w(t,"id",`subsection-${s[57]}-${s[60]}`)},m(y,_){h(y,t,_),E(t,e),E(e,n),E(n,i),M(o,i,null),E(n,l),E(n,a),F.m(a,null),E(n,r),N&&N.m(n,null),E(t,c),E(t,d);for(let A=0;A<p.length;A+=1)p[A]&&p[A].m(d,null);f=!0},p(y,_){const A={};128&_[0]&&(A.type=y[58].type),o.$set(A),v===(v=C(y))&&F?F.p(y,_):(F.d(1),F=v(y),F&&(F.c(),F.m(a,null))),!y[19]&&y[58].warning?N?(N.p(y,_),524416&_[0]&&u(N,1)):(N=tn(y),N.c(),u(N,1),N.m(n,null)):N&&(G(),m(N,1,1,()=>{N=null}),Y()),1080099752&_[0]|1&_[1]&&(b=dt(y[58].changes),G(),p=fn(p,_,$,1,y,b,k,d,pn,en,null,Je),Y())},i(y){if(!f){u(o.$$.fragment,y),u(N);for(let _=0;_<b.length;_+=1)u(p[_]);f=!0}},o(y){m(o.$$.fragment,y),m(N);for(let _=0;_<p.length;_+=1)m(p[_]);f=!1},d(y){y&&g(t),L(o),F.d(),N&&N.d();for(let _=0;_<p.length;_+=1)p[_].d()}}}function sn(s){let t,e,n,i,o,l,a,r,c,d,f,p,k;function C(P,H){return P[19]&&P[55].title==="Loading..."?mo:$o}let v=C(s),F=v(s);const N=[ho,go],b=[];function $(P,H){return P[19]&&P[55].description===""?0:1}a=$(s),r=b[a]=N[a](s);let y=s[57]===0&&function(P){let H,Z,Q,it,et;return Z=new Ft({props:{variant:"ghost-block",color:"neutral",size:2,$$slots:{default:[wo]},$$scope:{ctx:P}}}),Z.$on("click",P[29]),it=new Et({props:{content:P[25],$$slots:{default:[Lo]},$$scope:{ctx:P}}}),{c(){H=z("div"),D(Z.$$.fragment),Q=S(),D(it.$$.fragment),w(H,"class","c-diff-view__controls svelte-ibi4q5")},m(nt,X){h(nt,H,X),M(Z,H,null),E(H,Q),M(it,H,null),et=!0},p(nt,X){const ot={};134217728&X[0]|8&X[2]&&(ot.$$scope={dirty:X,ctx:nt}),Z.$set(ot);const pt={};33554432&X[0]&&(pt.content=nt[25]),52224&X[0]|8&X[2]&&(pt.$$scope={dirty:X,ctx:nt}),it.$set(pt)},i(nt){et||(u(Z.$$.fragment,nt),u(it.$$.fragment,nt),et=!0)},o(nt){m(Z.$$.fragment,nt),m(it.$$.fragment,nt),et=!1},d(nt){nt&&g(H),L(Z),L(it)}}}(s),_=(s[11]&&s[6].size>0||!s[11]&&s[23].size>0)&&s[57]===0&&Xe(ne(s)),A=dt(s[55].sections||[]),q=[];for(let P=0;P<A.length;P+=1)q[P]=nn(We(s,A,P));const V=P=>m(q[P],1,1,()=>{q[P]=null});return{c(){t=z("div"),e=z("div"),n=z("div"),i=z("h5"),F.c(),o=S(),l=z("div"),r.c(),c=S(),y&&y.c(),d=S(),_&&_.c(),f=S();for(let P=0;P<q.length;P+=1)q[P].c();p=S(),w(i,"class","c-diff-view__title svelte-ibi4q5"),w(l,"class","c-diff-view__description svelte-ibi4q5"),w(n,"class","c-diff-view__content svelte-ibi4q5"),w(e,"class","c-diff-view__header svelte-ibi4q5"),w(t,"class","c-diff-view__section svelte-ibi4q5"),w(t,"id",`section-${s[57]}`)},m(P,H){h(P,t,H),E(t,e),E(e,n),E(n,i),F.m(i,null),E(n,o),E(n,l),b[a].m(l,null),E(e,c),y&&y.m(e,null),E(t,d),_&&_.m(t,null),E(t,f);for(let Z=0;Z<q.length;Z+=1)q[Z]&&q[Z].m(t,null);E(t,p),k=!0},p(P,H){v===(v=C(P))&&F?F.p(P,H):(F.d(1),F=v(P),F&&(F.c(),F.m(i,null)));let Z=a;if(a=$(P),a===Z?b[a].p(P,H):(G(),m(b[Z],1,1,()=>{b[Z]=null}),Y(),r=b[a],r?r.p(P,H):(r=b[a]=N[a](P),r.c()),u(r,1),r.m(l,null)),P[57]===0&&y.p(P,H),(P[11]&&P[6].size>0||!P[11]&&P[23].size>0)&&P[57]===0?_?(_.p(ne(P),H),8390720&H[0]&&u(_,1)):(_=Xe(ne(P)),_.c(),u(_,1),_.m(t,f)):_&&(G(),m(_,1,1,()=>{_=null}),Y()),1080624040&H[0]|1&H[1]){let Q;for(A=dt(P[55].sections||[]),Q=0;Q<A.length;Q+=1){const it=We(P,A,Q);q[Q]?(q[Q].p(it,H),u(q[Q],1)):(q[Q]=nn(it),q[Q].c(),u(q[Q],1),q[Q].m(t,p))}for(G(),Q=A.length;Q<q.length;Q+=1)V(Q);Y()}},i(P){if(!k){u(r),u(y),u(_);for(let H=0;H<A.length;H+=1)u(q[H]);k=!0}},o(P){m(r),m(y),m(_),q=q.filter(xn);for(let H=0;H<q.length;H+=1)m(q[H]);k=!1},d(P){P&&g(t),F.d(),b[a].d(),y&&y.d(),_&&_.d(),At(q,P)}}}function No(s){let t,e,n,i;return n=new te({}),{c(){t=R(`Apply All
                  `),e=z("div"),D(n.$$.fragment),w(e,"class","c-diff-view__controls__icon svelte-ibi4q5")},m(o,l){h(o,t,l),h(o,e,l),M(n,e,null),i=!0},i(o){i||(u(n.$$.fragment,o),i=!0)},o(o){m(n.$$.fragment,o),i=!1},d(o){o&&(g(t),g(e)),L(n)}}}function qo(s){let t,e,n;return e=new lt({props:{size:2,$$slots:{default:[Oo]},$$scope:{ctx:s}}}),{c(){t=z("div"),D(e.$$.fragment),w(t,"class","c-diff-view__applied svelte-ibi4q5")},m(i,o){h(i,t,o),M(e,t,null),n=!0},i(i){n||(u(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){i&&g(t),L(e)}}}function Eo(s){let t,e,n,i,o;return e=new Xt({props:{size:1,useCurrentColor:!0}}),i=new lt({props:{size:2,$$slots:{default:[Po]},$$scope:{ctx:s}}}),{c(){t=z("div"),D(e.$$.fragment),n=S(),D(i.$$.fragment),w(t,"class","c-diff-view__applying svelte-ibi4q5")},m(l,a){h(l,t,a),M(e,t,null),E(t,n),M(i,t,null),o=!0},i(l){o||(u(e.$$.fragment,l),u(i.$$.fragment,l),o=!0)},o(l){m(e.$$.fragment,l),m(i.$$.fragment,l),o=!1},d(l){l&&g(t),L(e),L(i)}}}function Oo(s){let t,e,n;return e=new de({props:{iconName:"check"}}),{c(){t=R(`Applied
                      `),D(e.$$.fragment)},m(i,o){h(i,t,o),M(e,i,o),n=!0},p:at,i(i){n||(u(e.$$.fragment,i),n=!0)},o(i){m(e.$$.fragment,i),n=!1},d(i){i&&g(t),L(e,i)}}}function Po(s){let t;return{c(){t=R("Applying...")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function Ro(s){let t,e,n,i;const o=[Eo,qo,No],l=[];function a(r,c){return r[10]?0:r[11]?1:2}return t=a(s),e=l[t]=o[t](s),{c(){e.c(),n=xt()},m(r,c){l[t].m(r,c),h(r,n,c),i=!0},p(r,c){let d=t;t=a(r),t!==d&&(G(),m(l[d],1,1,()=>{l[d]=null}),Y(),e=l[t],e||(e=l[t]=o[t](r),e.c()),u(e,1),e.m(n.parentNode,n))},i(r){i||(u(e),i=!0)},o(r){m(e),i=!1},d(r){r&&g(n),l[t].d(r)}}}function To(s){let t,e;return t=new Ft({props:{variant:"ghost-block",color:"neutral",size:2,disabled:s[10]||s[11]||s[13].length>0||!s[12],$$slots:{default:[Ro]},$$scope:{ctx:s}}}),t.$on("click",s[32]),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};15360&i[0]&&(o.disabled=n[10]||n[11]||n[13].length>0||!n[12]),3072&i[0]|8&i[2]&&(o.$$scope={dirty:i,ctx:n}),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function Vo(s){let t;return{c(){t=R("No files changed")},m(e,n){h(e,t,n)},d(e){e&&g(t)}}}function So(s){let t,e,n,i,o,l,a,r,c=s[20]&&Ge(s);const d=[oo,so],f=[];function p(v,F){return v[26]?0:1}function k(v){s[48](v)}n=p(s),i=f[n]=d[n](s);let C={applyAllChanges:s[33]};return s[24]!==void 0&&(C.showModal=s[24]),l=new to({props:C}),Mt.push(()=>Ht(l,"showModal",k)),{c(){t=z("div"),c&&c.c(),e=S(),i.c(),o=S(),D(l.$$.fragment),w(t,"class","c-diff-view svelte-ibi4q5")},m(v,F){h(v,t,F),c&&c.m(t,null),E(t,e),f[n].m(t,null),h(v,o,F),M(l,v,F),r=!0},p(v,F){v[20]?c?(c.p(v,F),1048576&F[0]&&u(c,1)):(c=Ge(v),c.c(),u(c,1),c.m(t,e)):c&&(G(),m(c,1,1,()=>{c=null}),Y());let N=n;n=p(v),n===N?f[n].p(v,F):(G(),m(f[N],1,1,()=>{f[N]=null}),Y(),i=f[n],i?i.p(v,F):(i=f[n]=d[n](v),i.c()),u(i,1),i.m(t,null));const b={};!a&&16777216&F[0]&&(a=!0,b.showModal=v[24],Bt(()=>a=!1)),l.$set(b)},i(v){r||(u(c),u(i),u(l.$$.fragment,v),r=!0)},o(v){m(c),m(i),m(l.$$.fragment,v),r=!1},d(v){v&&(g(t),g(o)),c&&c.d(),f[n].d(),L(l,v)}}}function jo(s,t,e){let n,i,o,l,a,r,c,d,f,{changedFiles:p}=t,{agentLabel:k}=t,{latestUserPrompt:C}=t,{onApplyChanges:v}=t,{onOpenFile:F}=t,{onRenderBackup:N}=t,{preloadedExplanation:b}=t,{isAgentFromDifferentRepo:$=!1}=t,{conflictFiles:y=new Set}=t;const _=Rt(Vt.key);let A="",q=!1,V=[],P=[],H=!1,Z=!1,Q=null,it=!0,et={},nt=[],X=!1,ot=!1,pt=!0,rt=new Set,_t=!1;const wt=Gt({});Lt(s,wt,x=>e(16,f=x));let $t={};function Dt(x,T){e(38,$t[x]=T,$t)}async function mt(x,T,U){if(v)return wt.update(j=>(j[x]="pending",j)),new Promise(j=>{v==null||v(x,T,U).then(()=>{wt.update(I=>(I[x]="applied",I)),j()})})}function kt(){if(!v)return;_.reportApplyChangesEvent(),e(10,X=!0),e(11,ot=!1);const{filesToApply:x,areAllPathsApplied:T}=ge(V,p,$t);T||x.length===0?e(11,ot=T):ei(x,mt).then(()=>{e(10,X=!1),e(11,ot=!0)})}function Ct(x){const T={title:"Changed Files",description:`${x.length} files were changed`,sections:[]},U=[],j=[],I=[];return x.forEach(J=>{J.old_path?J.new_path?j.push(J):I.push(J):U.push(J)}),U.length>0&&T.sections.push(W("Added files","feature",U)),j.length>0&&T.sections.push(W("Modified files","fix",j)),I.length>0&&T.sections.push(W("Deleted files","chore",I)),[T]}function W(x,T,U){const j=[];return U.forEach(I=>{const J=I.new_path||I.old_path,K=I.old_contents||"",B=I.new_contents||"",tt=I.old_path?I.old_path:"",st=se(tt,I.new_path||"/dev/null",K,B,"","",{context:3}),ut=`${Ot(J)}-${Ot(K+B)}`;j.push({id:ut,path:J,diff:st,originalCode:K,modifiedCode:B})}),{title:x,descriptions:[],type:T,changes:j}}async function St(){if(!q)return;if(e(18,H=!0),e(19,Z=!1),e(20,Q=null),e(17,P=[]),e(7,V=[]),a)return void e(18,H=!1);const x=102400;let T=0;if(p.forEach(U=>{var j,I;T+=(((j=U.old_contents)==null?void 0:j.length)||0)+(((I=U.new_contents)==null?void 0:I.length)||0)}),p.length>12||T>512e3){try{e(7,V=Ct(p))}catch(U){console.error("Failed to create simple explanation:",U),e(20,Q="Failed to create explanation for large changes.")}e(18,H=!1)}else try{const U=new ni(J=>un.postMessage(J)),j=new Map,I=p.map(J=>{const K=J.new_path||J.old_path,B=J.old_contents||"",tt=J.new_contents||"",st=`${Ot(K)}-${Ot(B+tt)}`;return j.set(st,{old_path:J.old_path,new_path:J.new_path,old_contents:B,new_contents:tt,change_type:J.change_type}),{id:st,old_path:J.old_path,new_path:J.new_path,change_type:J.change_type}});try{const J=I.length===1;let K=[];J?K=I.map(B=>({path:B.new_path||B.old_path,changes:[{id:B.id,path:B.new_path||B.old_path,diff:`File: ${B.new_path||B.old_path}
Change type: ${B.change_type||"modified"}`,originalCode:"",modifiedCode:""}]})):K=(await U.send({type:"get-diff-group-changes-request",data:{changedFiles:I,changesById:!0,apikey:A}},3e4)).data.groupedChanges,e(17,P=K.map(B=>({path:B.path,changes:B.changes.map(tt=>{if(tt.id&&j.has(tt.id)){const st=j.get(tt.id);let ut=tt.diff;return ut&&!ut.startsWith("File:")||(ut=se(st.old_path||"",st.new_path||"",st.old_contents||"",st.new_contents||"")),{...tt,diff:ut,old_path:st.old_path,new_path:st.new_path,old_contents:st.old_contents,new_contents:st.new_contents,change_type:st.change_type,originalCode:st.old_contents||"",modifiedCode:st.new_contents||""}}return tt})})))}catch(J){console.error("Failed to group changes with LLM, falling back to simple grouping:",J);try{const K=I.map(B=>{if(B.id&&j.has(B.id)){const tt=j.get(B.id);return{...B,old_path:tt.old_path,new_path:tt.new_path,old_contents:tt.old_contents||"",new_contents:tt.new_contents||"",change_type:tt.change_type}}return B});e(7,V=Ct(K)),e(17,P=V[0].sections.map(B=>({path:B.title,changes:B.changes}))),e(19,Z=!1)}catch(K){console.error("Failed to create simple explanation:",K),e(20,Q="Failed to group changes. Please try again.")}}if(e(18,H=!1),!P||P.length===0)throw new Error("Failed to group changes");if(!V||V.length===0){e(7,V=function(K){const B={title:"Loading...",description:"",sections:[]};return K.forEach(tt=>{const st=tt.changes.map(bt=>{if(bt.id)return bt;const zt=Ot(bt.path),Nt=Ot(bt.originalCode+bt.modifiedCode);return{...bt,id:`${zt}-${Nt}`}}),ut={title:tt.path,descriptions:[],type:"other",changes:st};B.sections.push(ut)}),[B]}(P));const J=V[0].sections.map(K=>({path:K.title,changes:K.changes.map(B=>{var bt,zt,Nt;const tt=((bt=B.originalCode)==null?void 0:bt.length)||0,st=((zt=B.modifiedCode)==null?void 0:zt.length)||0,ut=((Nt=B.diff)==null?void 0:Nt.length)||0;return tt>x||st>x||ut>x?{id:B.id,path:B.path,diff:`File: ${B.path}
Content too large to include in explanation request (${Math.max(tt,st,ut)} bytes)`,originalCode:tt>x?`[File content too large: ${tt} bytes]`:B.originalCode,modifiedCode:st>x?`[File content too large: ${st} bytes]`:B.modifiedCode}:{id:B.id,path:B.path,diff:B.diff,originalCode:B.originalCode,modifiedCode:B.modifiedCode}})}));e(19,Z=!0);try{const{explanation:K,error:B}=await _.getDescriptions(J,A);if(B==="Token limit exceeded")return e(7,V=Ct(p)),e(18,H=!1),void e(19,Z=!1);K&&K.length>0&&K.forEach((tt,st)=>{tt.sections&&tt.sections.forEach((ut,bt)=>{ut.changes&&ut.changes.forEach(zt=>{const Nt=V[st];if(Nt&&Nt.sections){const ee=Nt.sections[bt];if(ee&&ee.changes){const It=ee.changes.find(bn=>bn.id===zt.id);It&&(zt.originalCode=It.originalCode,zt.modifiedCode=It.modifiedCode,zt.diff=It.diff)}}})})}),e(7,V=K)}catch(K){console.error("Failed to get descriptions, using skeleton explanation:",K)}}V.length===0&&e(20,Q="Failed to generate explanation.")}catch(U){console.error("Failed to get explanation:",U),e(20,Q=U instanceof Error?U.message:"An error occurred while generating the explanation.")}finally{e(18,H=!1),e(19,Z=!1)}}Zt(()=>{const x=localStorage.getItem("anthropic_apikey");x&&(A=x),e(37,q=!0)});let Pt="",O="Apply all changes locally";return s.$$set=x=>{"changedFiles"in x&&e(0,p=x.changedFiles),"agentLabel"in x&&e(1,k=x.agentLabel),"latestUserPrompt"in x&&e(2,C=x.latestUserPrompt),"onApplyChanges"in x&&e(35,v=x.onApplyChanges),"onOpenFile"in x&&e(3,F=x.onOpenFile),"onRenderBackup"in x&&e(4,N=x.onRenderBackup),"preloadedExplanation"in x&&e(36,b=x.preloadedExplanation),"isAgentFromDifferentRepo"in x&&e(5,$=x.isAgentFromDifferentRepo),"conflictFiles"in x&&e(6,y=x.conflictFiles)},s.$$.update=()=>{if(65537&s.$$.dirty[0]&&p&&wt.set(p.reduce((x,T)=>{const U=T.new_path||T.old_path;return x[U]=f[U]??"none",x},{})),1&s.$$.dirty[0]&&e(41,r=JSON.stringify(p)),1376&s.$$.dirty[1]&&q&&r&&r!==Pt&&(e(39,Pt=r),b&&b.length>0?(e(7,V=b),e(18,H=!1),e(19,Z=!1)):St(),e(10,X=!1),e(11,ot=!1),e(38,$t={})),896&s.$$.dirty[0]&&V&&V.length>0){const x=Jt(V);Array.from(x).forEach(j=>{et[j]===void 0&&e(9,et[j]=!it,et)});const T=Object.keys(et).filter(j=>et[j]),U=Array.from(x);U.length>0&&e(8,it=!U.some(j=>T.includes(j)))}if(512&s.$$.dirty[0]&&e(27,n=Object.values(et).some(Boolean)),128&s.$$.dirty[0]|128&s.$$.dirty[1]&&V&&V.length>0&&V.flatMap(x=>x.sections||[]).flatMap(x=>x.changes).forEach(x=>{$t[x.path]||e(38,$t[x.path]=x.modifiedCode,$t)}),128&s.$$.dirty[0]&&e(40,i=JSON.stringify(V)),65664&s.$$.dirty[0]|512&s.$$.dirty[1]&&e(12,o=(()=>{if(i&&f){const x=Jt(V);return x.size!==0&&Array.from(x).some(T=>f[T]!=="applied")}return!1})()),65536&s.$$.dirty[0]&&e(11,ot=Object.keys(f).every(x=>f[x]==="applied")),65536&s.$$.dirty[0]&&e(13,l=Object.keys(f).filter(x=>f[x]==="pending")),129&s.$$.dirty[0]|128&s.$$.dirty[1]&&async function(x,T,U){const{filesToApply:j}=ge(x,T,U),I=new Set;for(const J of j)(await _.previewApplyChanges(J.path,J.originalCode,J.newCode)).hasConflicts&&I.add(J.path);e(23,rt=I)}(V,p,$t),1&s.$$.dirty[0]&&e(26,a=p.length===0),67712&s.$$.dirty[0]|512&s.$$.dirty[1]&&i&&ot){const x=Jt(V);Array.from(x).every(T=>f[T]==="applied")||e(11,ot=!1)}2112&s.$$.dirty[0]&&e(14,c=ot&&y.size>0),15392&s.$$.dirty[0]&&e(15,d=$||X||ot||l.length>0||!o),64544&s.$$.dirty[0]&&(d?$?e(25,O="Cannot apply changes from a different repository locally"):X?e(25,O="Applying changes..."):c?e(25,O="All changes applied, but conflicts need to be resolved manually"):ot?e(25,O="All changes applied"):l.length>0?e(25,O="Waiting for changes to apply"):o||e(25,O="No changes to apply"):e(25,O="Apply all changes locally"))},[p,k,C,F,N,$,y,V,it,et,X,ot,o,l,c,d,f,P,H,Z,Q,nt,pt,rt,_t,O,a,n,wt,function(){const x=Jt(V),T=Object.values(et).some(Boolean);e(8,it=T),Array.from(x).forEach(U=>{e(9,et[U]=!it,et)})},Dt,mt,async function(){const x=await _.canApplyChanges();x.canApply?kt():x.hasUnstagedChanges&&e(24,_t=!0)},kt,St,v,b,q,$t,Pt,i,r,(x,T)=>{Dt(x.path,T)},x=>{mt(x.path,x.originalCode,x.modifiedCode)},x=>F(x.path),function(x,T){s.$$.not_equal(et[T.path],x)&&(et[T.path]=x,e(9,et),e(7,V),e(8,it),e(37,q),e(41,r),e(39,Pt),e(36,b),e(0,p))},function(x,T,U,j){Mt[x?"unshift":"push"](()=>{nt[100*T+10*U+j.path.length%10]=x,e(21,nt)})},function(x){pt=x,e(22,pt)},function(x){_t=x,e(24,_t)}]}class Ho extends vt{constructor(t){super(),yt(this,t,jo,So,ht,{changedFiles:0,agentLabel:1,latestUserPrompt:2,onApplyChanges:35,onOpenFile:3,onRenderBackup:4,preloadedExplanation:36,isAgentFromDifferentRepo:5,conflictFiles:6},null,[-1,-1,-1])}}function on(s){let t,e,n=s[8].opts,i=ln(s);return{c(){t=z("div"),i.c(),w(t,"class","file-explorer-contents svelte-5tfpo4")},m(o,l){h(o,t,l),i.m(t,null),e=!0},p(o,l){256&l&&ht(n,n=o[8].opts)?(G(),m(i,1,1,at),Y(),i=ln(o),i.c(),u(i,1),i.m(t,null)):i.p(o,l)},i(o){e||(u(i),e=!0)},o(o){m(i),e=!1},d(o){o&&g(t),i.d(o)}}}function Bo(s){var n,i;let t,e;return t=new Ho({props:{changedFiles:s[0],onApplyChanges:s[10],onOpenFile:s[11],agentLabel:s[3],latestUserPrompt:s[4],onRenderBackup:s[12],preloadedExplanation:(i=(n=s[8])==null?void 0:n.opts)==null?void 0:i.preloadedExplanation,isAgentFromDifferentRepo:s[5],conflictFiles:s[6]}}),{c(){D(t.$$.fragment)},m(o,l){M(t,o,l),e=!0},p(o,l){var r,c;const a={};1&l&&(a.changedFiles=o[0]),8&l&&(a.agentLabel=o[3]),16&l&&(a.latestUserPrompt=o[4]),128&l&&(a.onRenderBackup=o[12]),256&l&&(a.preloadedExplanation=(c=(r=o[8])==null?void 0:r.opts)==null?void 0:c.preloadedExplanation),32&l&&(a.isAgentFromDifferentRepo=o[5]),64&l&&(a.conflictFiles=o[6]),t.$set(a)},i(o){e||(u(t.$$.fragment,o),e=!0)},o(o){m(t.$$.fragment,o),e=!1},d(o){L(t,o)}}}function Uo(s){let t,e;return t=new Fs({props:{changedFiles:s[0],onApplyChanges:s[10],onOpenFile:s[11],pendingFiles:s[1],appliedFiles:s[2]}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};1&i&&(o.changedFiles=n[0]),2&i&&(o.pendingFiles=n[1]),4&i&&(o.appliedFiles=n[2]),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function ln(s){let t,e,n,i;const o=[Uo,Bo],l=[];function a(r,c){return r[7]==="changedFiles"?0:1}return t=a(s),e=l[t]=o[t](s),{c(){e.c(),n=xt()},m(r,c){l[t].m(r,c),h(r,n,c),i=!0},p(r,c){let d=t;t=a(r),t===d?l[t].p(r,c):(G(),m(l[d],1,1,()=>{l[d]=null}),Y(),e=l[t],e?e.p(r,c):(e=l[t]=o[t](r),e.c()),u(e,1),e.m(n.parentNode,n))},i(r){i||(u(e),i=!0)},o(r){m(e),i=!1},d(r){r&&g(n),l[t].d(r)}}}function Zo(s){let t,e,n,i=s[0]&&on(s);return{c(){t=z("div"),e=z("div"),i&&i.c(),w(e,"class","file-explorer-main svelte-5tfpo4"),w(t,"class","diff-page svelte-5tfpo4")},m(o,l){h(o,t,l),E(t,e),i&&i.m(e,null),n=!0},p(o,[l]){o[0]?i?(i.p(o,l),1&l&&u(i,1)):(i=on(o),i.c(),u(i,1),i.m(e,null)):i&&(G(),m(i,1,1,()=>{i=null}),Y())},i(o){n||(u(i),n=!0)},o(o){m(i),n=!1},d(o){o&&g(t),i&&i.d()}}}function Io(s,t,e){let n,{changedFiles:i=[]}=t,{pendingFiles:o=[]}=t,{appliedFiles:l=[]}=t,{agentLabel:a}=t,{latestUserPrompt:r}=t,{isAgentFromDifferentRepo:c=!1}=t,d=new Set;const f=Rt(Vt.key),p=Rt(Qt.key);Lt(s,p,C=>e(8,n=C));let k="summary";return function(C){C.subscribe(v=>{if(v){const F=document.getElementById(le(v));F&&F.scrollIntoView({behavior:"smooth",block:"center"})}})}(function(C=null){const v=Gt(C);return ie(vn,v),v}(null)),s.$$set=C=>{"changedFiles"in C&&e(0,i=C.changedFiles),"pendingFiles"in C&&e(1,o=C.pendingFiles),"appliedFiles"in C&&e(2,l=C.appliedFiles),"agentLabel"in C&&e(3,a=C.agentLabel),"latestUserPrompt"in C&&e(4,r=C.latestUserPrompt),"isAgentFromDifferentRepo"in C&&e(5,c=C.isAgentFromDifferentRepo)},[i,o,l,a,r,c,d,k,n,p,async(C,v,F)=>{const{success:N,hasConflicts:b}=await f.applyChanges(C,v,F);N&&b&&e(6,d=new Set([...d,C]))},C=>f.openFile(C),()=>{e(7,k="changedFiles")}]}class Wo extends vt{constructor(t){super(),yt(this,t,Io,Zo,ht,{changedFiles:0,pendingFiles:1,appliedFiles:2,agentLabel:3,latestUserPrompt:4,isAgentFromDifferentRepo:5})}}function Jo(s){let t,e,n,i,o;return e=new Xt({props:{size:1}}),{c(){t=z("div"),D(e.$$.fragment),n=S(),i=z("p"),i.textContent="Loading diff view...",w(t,"class","l-center svelte-ccste2")},m(l,a){h(l,t,a),M(e,t,null),E(t,n),E(t,i),o=!0},p:at,i(l){o||(u(e.$$.fragment,l),o=!0)},o(l){m(e.$$.fragment,l),o=!1},d(l){l&&g(t),L(e)}}}function Go(s){let t,e;return t=new Wo({props:{changedFiles:s[0].changedFiles,agentLabel:s[0].sessionSummary,latestUserPrompt:s[0].userPrompt,pendingFiles:s[3].applyingFilePaths||[],appliedFiles:s[3].appliedFilePaths||[],isAgentFromDifferentRepo:s[0].isAgentFromDifferentRepo||!1}}),{c(){D(t.$$.fragment)},m(n,i){M(t,n,i),e=!0},p(n,i){const o={};1&i&&(o.changedFiles=n[0].changedFiles),1&i&&(o.agentLabel=n[0].sessionSummary),1&i&&(o.latestUserPrompt=n[0].userPrompt),1&i&&(o.isAgentFromDifferentRepo=n[0].isAgentFromDifferentRepo||!1),t.$set(o)},i(n){e||(u(t.$$.fragment,n),e=!0)},o(n){m(t.$$.fragment,n),e=!1},d(n){L(t,n)}}}function Yo(s){let t,e,n,i;const o=[Go,Jo],l=[];function a(r,c){return r[0]?0:1}return e=a(s),n=l[e]=o[e](s),{c(){t=z("div"),n.c(),w(t,"class","l-main svelte-ccste2")},m(r,c){h(r,t,c),l[e].m(t,null),i=!0},p(r,c){let d=e;e=a(r),e===d?l[e].p(r,c):(G(),m(l[d],1,1,()=>{l[d]=null}),Y(),n=l[e],n?n.p(r,c):(n=l[e]=o[e](r),n.c()),u(n,1),n.m(t,null))},i(r){i||(u(n),i=!0)},o(r){m(n),i=!1},d(r){r&&g(t),l[e].d()}}}function Ko(s){let t,e,n,i;return t=new In.Root({props:{$$slots:{default:[Yo]},$$scope:{ctx:s}}}),{c(){D(t.$$.fragment)},m(o,l){M(t,o,l),e=!0,n||(i=jt(window,"message",s[1].onMessageFromExtension),n=!0)},p(o,[l]){const a={};33&l&&(a.$$scope={dirty:l,ctx:o}),t.$set(a)},i(o){e||(u(t.$$.fragment,o),e=!0)},o(o){m(t.$$.fragment,o),e=!1},d(o){L(t,o),n=!1,i()}}}function Qo(s,t,e){let n,i,o=new Nn(un),l=new Qt(o);Lt(s,l,r=>e(4,i=r)),o.registerConsumer(l);let a=new Vt(o);return ie(Vt.key,a),ie(Qt.key,l),Zt(()=>(l.onPanelLoaded(),()=>{o.dispose()})),s.$$.update=()=>{16&s.$$.dirty&&e(0,n=i.opts)},[n,o,l,a,i]}new class extends vt{constructor(s){super(),yt(this,s,Qo,Ko,ht,{})}}({target:document.getElementById("app")});
