<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Preference</title>
    <script nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <script type="module" crossorigin src="./assets/preference-CGg3fXaM.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BRONhjN7.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-DYitSqXV.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-8Nye8RQz.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-Bq1AWULp.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-qUvJDeai.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-emviP3SE.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-CfWKsX8l.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-B7q3rq9x.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/index-DL51nDXl.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/folder-opened-UvJxBo0O.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/index-BS1SrqoN.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-Dv6uf1Os.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/toggleHighContrast-Cb9MCs64.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/index-BcKMBZ3o.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/SimpleMonaco-zl8B1j3Y.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/diff-operations-CLDm-KfY.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/await_block-eLW-gO_q.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/chat-context-BNzKQhCT.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/index-B528snJk.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/remote-agents-client-oncIrPGY.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/ra-diff-ops-model-CV6PzMi8.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-YH5RXu5p.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-DLatYyzj.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/OpenFileButton-DaEVNMZt.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-mSbxnQHz.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/partner-mcp-utils-DEO975Yb.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/MaterialIcon-DRaAkbsC.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/CopyButton-BImWfLKc.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-BgT8k3VD.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/LanguageIcon-D8OInxb5.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/next-edit-types-904A5ehg.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/IconFilePath-BmqHsAaQ.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/Filespan-Fjc1Eq8J.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/index-QgOjpL-u.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-BlyUob3E.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-BZYwtsmR.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-BmFLDXsf.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/check-BJ4Ab2Wq.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/AugmentMessage-D7idHus4.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-Bk4JGPpB.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B8PGiArX.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/AugmentMessage-DDGpGAxt.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/folder-opened-hTsrGIsd.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-pxiddGnV.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-C__BzIz3.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/index-DeNt0W4u.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-BzgVnrJx.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/diff-operations-DTcQ2vsq.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/toggleHighContrast-D4zjdeIP.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/OpenFileButton-Bx7Exmp0.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-CWo9IUaY.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-KGoGPe8_.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-DokFokeT.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/partner-mcp-utils-BRhZ8F7l.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/MaterialIcon-BO_oU5T3.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/CopyButton-B0_wR17F.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/IconFilePath-CiKel2Kp.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/LanguageIcon-D78BqCXT.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/Filespan-CMsGeQ5J.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/preference-6d_5zsjM.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
