<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - Tool Configuration</title>
    <script nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <script type="module" crossorigin src="./assets/settings-Ppc8asuJ.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-BRONhjN7.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-DYitSqXV.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-qUvJDeai.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-emviP3SE.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-CfWKsX8l.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-Bq1AWULp.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-B7q3rq9x.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/index-DL51nDXl.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/partner-mcp-utils-DEO975Yb.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/download-CkkAwTb2.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-Bsn_YzZH.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-CGIXiTeX.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/index-QgOjpL-u.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-BgT8k3VD.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/Drawer-CPSN0HnX.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-YH5RXu5p.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-8Nye8RQz.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-BZYwtsmR.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-DLatYyzj.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-mSbxnQHz.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/chevron-down-BmFLDXsf.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/index-BS1SrqoN.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/index-BcKMBZ3o.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/lodash-E-8fRYvv.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/rules-model-CwG-l8cG.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/RulesModeSelector-DgNhWqvf.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="modulepreload" crossorigin href="./assets/ModalAugment-CGgB_q36.js" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-Bk4JGPpB.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/download-Bd_7IRvZ.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-DgOX1UWm.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-B8PGiArX.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/partner-mcp-utils-BRhZ8F7l.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/index-DeNt0W4u.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/Drawer-DwFbLE28.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-KGoGPe8_.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-C__BzIz3.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-pxiddGnV.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-CWo9IUaY.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-DokFokeT.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-BzgVnrJx.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/rules-model-B6vv3aGc.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/RulesModeSelector-Qv_62MPy.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/ModalAugment-CM3byOYD.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
    <link rel="stylesheet" crossorigin href="./assets/settings-sJv-jASV.css" nonce="nonce-hbII6HlnHorJlYh63I+KdA==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
