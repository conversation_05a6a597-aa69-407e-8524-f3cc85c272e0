var v=Object.defineProperty;var A=(n,e,t)=>e in n?v(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var m=(n,e,t)=>A(n,typeof e!="symbol"?e+"":e,t);import{F as h,R as d,a as u}from"./index-B528snJk.js";import{C as k,a as I}from"./message-broker-emviP3SE.js";import{W as o,R as B}from"./IconButtonAugment-DYitSqXV.js";import{S as P,a as E}from"./types-CGlLNakm.js";function C(n){return function(e){try{if(isNaN(e.getTime()))return"Unknown time";const t=new Date().getTime()-e.getTime(),s=Math.floor(t/1e3),r=Math.floor(s/60),a=Math.floor(r/60),i=Math.floor(a/24);return s<60?`${s}s ago`:r<60?`${r}m ago`:a<24?`${a}h ago`:i<30?`${i}d ago`:e.toLocaleDateString()}catch(t){return console.error("Error formatting date:",t),"Unknown time"}}(new Date(n))}function b(n,e){let t=1e3;const s=new Date(n),r=setInterval(()=>{const a=Math.floor((new Date().getTime()-s.getTime())/1e3/60);a>=1&&(t=6e4),a>=60&&(t=36e5),a>=1440&&(t=864e5),e(C(n))},t);return()=>clearInterval(r)}function N(n,e){const t=setInterval(()=>{const s=n.getTime()-Date.now();if(s<=0)return void clearInterval(t);const r=Math.floor(s/1e3),a=Math.floor(r/60),i=Math.floor(a/60),c=Math.floor(i/24);e(r<60?`${r}s`:a<60?`${a}m ${r%60}s`:i<24?`${i}h`:c<30?`${c}d`:"1mo")},1e3);return()=>clearInterval(t)}function U(n){if(n===void 0)return"neutral";switch(n){case d.agentPending:case d.agentStarting:case d.agentRunning:return"info";case d.agentIdle:return"success";case d.agentFailed:return"error";default:return"neutral"}}function W(n){if(n===void 0)return"neutral";switch(n){case u.workspaceRunning:return"info";case u.workspacePausing:case u.workspacePaused:case u.workspaceResuming:default:return"neutral"}}function j(n,e,t){if(e===u.workspaceResuming)return"Resuming";switch(n){case d.agentStarting:return"Starting";case d.agentRunning:return"Running";case d.agentIdle:return t?"Unread":"Idle";case d.agentPending:return"Pending";case d.agentFailed:return"Failed";default:return"Unknown"}}function G(n){switch(n){case"Unread":return"accent";case"Starting":case"Resuming":return"iris";case"Running":return"success";case"Idle":case"Pending":default:return"neutral";case"Failed":return"error"}}const p=n=>q(n),q=n=>{const e={};for(const a of n){const i=a.old_path||a.new_path;if(e[i])e[i].finalExists=a.new_path!=="",e[i].finalContent=a.new_path!==""?a.new_contents:"",e[i].finalPath=a.new_path||a.old_path,e[i].latestChange=a,a.change_type===h.deleted&&a.old_contents!==""&&(e[i].originalContent=a.old_contents);else{const c=a.old_path!=="";e[i]={originalExists:c,originalContent:c?a.old_contents:"",finalExists:a.new_path!=="",finalContent:a.new_path!==""?a.new_contents:"",finalPath:a.new_path||a.old_path,latestChange:a}}}const t=[];for(const[a,i]of Object.entries(e))if(i.originalExists!==i.finalExists||i.originalExists&&i.finalExists&&i.originalContent!==i.finalContent){const c={id:i.latestChange.id,old_path:i.originalExists?a:"",new_path:i.finalExists?i.finalPath:"",old_contents:i.originalContent,new_contents:i.finalContent,change_type:(s=i.originalExists,r=i.finalExists,!s&&r?h.added:s&&!r?h.deleted:h.modified)};t.push(c)}var s,r;return t},V=n=>{const e=n.flatMap(t=>t.changed_files);return p(e)},z=(n,e)=>{var s;const t=y(n,e);return((s=n[t])==null?void 0:s.exchange.request_message)??""},y=(n,e)=>{var t;return e<0||e>=n.length?-1:(t=n[e])!=null&&t.exchange.request_message?e:n.slice(0,e).findLastIndex(s=>s.exchange.request_message)},S=(n,e)=>{const t=n.slice(e+1).findIndex(s=>s.exchange.request_message);return t===-1?n.length:e+t+1},J=(n,e)=>{if(e<0||e>=n.length)return[];if(y(n,e)===-1){const r=n.flatMap(a=>a.changed_files);return p(r)}const t=((r,a)=>{const i=y(r,a);let c=S(r,a);const g=i===-1?0:i+1;return r.slice(g,c)})(n,e),s=t.flatMap(r=>r.changed_files);return p(s)},K=(n,e)=>{var c,g;const t=S(n,e),s=n.slice(e,t),r=(c=n[e].exchange.response_nodes)==null?void 0:c.find(l=>l.type===k.TOOL_USE);if(!r)return[];const a=(g=r.tool_use)==null?void 0:g.tool_use_id;if(!a)return[];const i=s.find(l=>{var R;return(R=l.exchange.request_nodes)==null?void 0:R.some(f=>{var _;return f.type===I.TOOL_RESULT&&((_=f.tool_result_node)==null?void 0:_.tool_use_id)===a})});return i?i.changed_files:[]};class x extends Error{constructor(e){super(e),this.name="StreamRetryExhaustedError"}}class w{constructor(e,t,s,r,a=5,i=4e3,c=2,g){m(this,"_isCancelled",!1);m(this,"_isExhausted",!1);m(this,"streamId");this.agentId=e,this.lastProcessedValue=t,this.startStreamFn=s,this.cancelStreamFn=r,this.maxRetries=a,this.baseDelay=i,this.attemptErrorThreshold=c,this.unhandledErrorMessage=g,this.streamId=crypto.randomUUID()}get isCancelled(){return this._isCancelled}get isExhausted(){return this._isExhausted}async cancel(){this._isCancelled=!0,await this.cancelStreamFn(this.streamId)}async*getStream(){let e=0;for(;!this._isCancelled;)try{const t=this.startStreamFn(this.agentId,this.streamId,this.lastProcessedValue);for await(const s of t){if(this._isCancelled)return;e=0,yield s}return}catch(t){const s=t instanceof Error?t.message:String(t);if(s===P&&(this._isCancelled=!0),this._isCancelled)return;if(e++,e>this.maxRetries)throw this._isExhausted=!0,new x(`Failed after ${this.maxRetries} attempts: ${s}`);let r=this.baseDelay*2**(e-1);s===E?r=0:e>this.attemptErrorThreshold&&(yield{errorMessage:this.unhandledErrorMessage??s,retryAt:new Date(Date.now()+r)}),console.warn(`Retrying remote agent stream in ${r/1e3} seconds... (Attempt ${e} of ${this.maxRetries})`),await new Promise(a=>setTimeout(a,r));continue}}}const Q=n=>n!=null&&n.updates!==void 0;class O extends w{constructor(e,t,s,r,a=5,i=4e3){super(e,t,s,r,a,i,1,"There was an error connecting to the remote agent.")}}const X=n=>n!=null&&n.updates!==void 0;class H extends w{constructor(e,t,s,r=5,a=4e3){super("overviews",e,t,s,r,a,2,void 0)}}class M{constructor(e){m(this,"_msgBroker");m(this,"_activeRetryStreams",new Map);m(this,"_activeOverviewsStream");this._msgBroker=e}hasActiveHistoryStream(e){return this._activeRetryStreams.has(e)}getActiveHistoryStream(e){return this._activeRetryStreams.get(e)}get activeHistoryStreams(){return this._activeRetryStreams}hasActiveOverviewsStream(){return this._activeOverviewsStream!==void 0&&!this._activeOverviewsStream.isCancelled}getActiveOverviewsStream(){return this._activeOverviewsStream}async sshToRemoteAgent(e){const t=await this._msgBroker.send({type:o.remoteAgentSshRequest,data:{agentId:e}},1e4);return!!t.data.success||(console.error("Failed to connect to remote agent:",t.data.error),!1)}async deleteRemoteAgent(e,t=!1){return(await this._msgBroker.send({type:o.deleteRemoteAgentRequest,data:{agentId:e,doSkipConfirmation:t}},1e4)).data.success}showRemoteAgentHomePanel(){this._msgBroker.postMessage({type:o.showRemoteAgentHomePanel})}closeRemoteAgentHomePanel(){this._msgBroker.postMessage({type:o.closeRemoteAgentHomePanel})}async getRemoteAgentNotificationEnabled(e){return(await this._msgBroker.send({type:o.getRemoteAgentNotificationEnabledRequest,data:{agentIds:e}})).data}async setRemoteAgentNotificationEnabled(e,t){await this._msgBroker.send({type:o.setRemoteAgentNotificationEnabled,data:{agentId:e,enabled:t}})}async deleteRemoteAgentNotificationEnabled(e){await this._msgBroker.send({type:o.deleteRemoteAgentNotificationEnabled,data:{agentId:e}})}async notifyRemoteAgentReady(e){await this._msgBroker.send({type:o.remoteAgentNotifyReady,data:e})}showRemoteAgentDiffPanel(e){this._msgBroker.postMessage({type:o.showRemoteAgentDiffPanel,data:e})}closeRemoteAgentDiffPanel(){this._msgBroker.postMessage({type:o.closeRemoteAgentDiffPanel})}async getRemoteAgentChatHistory(e,t,s=1e4){return await this._msgBroker.send({type:o.getRemoteAgentChatHistoryRequest,data:{agentId:e,lastProcessedSequenceId:t}},s)}async sendRemoteAgentChatRequest(e,t,s=9e4){return this._msgBroker.send({type:o.remoteAgentChatRequest,data:{agentId:e,requestDetails:t,timeoutMs:s}},s)}async interruptRemoteAgent(e,t=1e4){return await this._msgBroker.send({type:o.remoteAgentInterruptRequest,data:{agentId:e}},t)}async createRemoteAgent(e,t,s,r,a,i,c=1e4){return await this._msgBroker.send({type:o.createRemoteAgentRequest,data:{prompt:e,workspaceSetup:t,setupScript:s,isSetupScriptAgent:r,modelId:a,remoteAgentCreationMetrics:i}},c)}async getRemoteAgentOverviews(e=1e4){return await this._msgBroker.send({type:o.getRemoteAgentOverviewsRequest},e)}async listSetupScripts(e=5e3){return await this._msgBroker.send({type:o.listSetupScriptsRequest},e)}async saveSetupScript(e,t,s,r=5e3){return await this._msgBroker.send({type:o.saveSetupScriptRequest,data:{name:e,content:t,location:s}},r)}async deleteSetupScript(e,t,s=5e3){return await this._msgBroker.send({type:o.deleteSetupScriptRequest,data:{name:e,location:t}},s)}async renameSetupScript(e,t,s,r=5e3){return await this._msgBroker.send({type:o.renameSetupScriptRequest,data:{oldName:e,newName:t,location:s}},r)}async getRemoteAgentWorkspaceLogs(e,t,s,r=1e4){return await this._msgBroker.send({type:o.remoteAgentWorkspaceLogsRequest,data:{agentId:e,lastProcessedStep:t,lastProcessedSequenceId:s}},r)}async saveLastRemoteAgentSetup(e,t,s){return await this._msgBroker.send({type:o.saveLastRemoteAgentSetupRequest,data:{lastRemoteAgentGitRepoUrl:e,lastRemoteAgentGitBranch:t,lastRemoteAgentSetupScript:s}})}async getLastRemoteAgentSetup(){return await this._msgBroker.send({type:o.getLastRemoteAgentSetupRequest})}async*startRemoteAgentOverviewsStream(e,t,s=6e4,r=3e5){const a={type:o.remoteAgentOverviewsStreamRequest,data:{streamId:e,lastUpdateTimestamp:t}},i=this._msgBroker.stream(a,s,r);for await(const c of i){if(c.data.error)throw new Error(c.data.error);yield c.data.response}}async*startRemoteAgentHistoryStream(e,t,s,r=6e4,a=3e5){const i={type:o.remoteAgentHistoryStreamRequest,data:{streamId:t,agentId:e,lastProcessedSequenceId:s}},c=this._msgBroker.stream(i,r,a);for await(const g of c)yield g.data}async*startRemoteAgentsListStreamWithRetry(e,t=5,s=4e3){var a;const r=new H(e,(i,c,g)=>this.startRemoteAgentOverviewsStream(c,g),i=>this._closeRemoteAgentsStream(i),t,s);(a=this._activeOverviewsStream)==null||a.cancel(),this._activeOverviewsStream=r;try{yield*r.getStream()}finally{r.isCancelled||r.isExhausted||(this._activeOverviewsStream=void 0)}}async*startRemoteAgentHistoryStreamWithRetry(e,t,s=5,r=4e3){var i;const a=new O(e,t,(c,g,l)=>this.startRemoteAgentHistoryStream(c,g,l),c=>this._closeRemoteAgentsStream(c),s,r);(i=this._activeRetryStreams.get(e))==null||i.cancel(),this._activeRetryStreams.set(e,a);try{yield*a.getStream()}finally{a.isCancelled||this._activeRetryStreams.delete(e)}}cancelRemoteAgentOverviewsStream(){this._activeOverviewsStream&&(this._activeOverviewsStream.cancel(),this._activeOverviewsStream=void 0)}cancelRemoteAgentHistoryStream(e){const t=this._activeRetryStreams.get(e);t&&(t.cancel(),this._activeRetryStreams.delete(e))}async _closeRemoteAgentsStream(e){await this._msgBroker.send({type:o.cancelRemoteAgentsStreamRequest,data:{streamId:e}})}cancelAllRemoteAgentHistoryStreams(){this._activeRetryStreams.forEach(e=>{e.cancel()}),this._activeRetryStreams.clear()}dispose(){this.cancelRemoteAgentOverviewsStream(),this.cancelAllRemoteAgentHistoryStreams()}async getPinnedAgentsFromStore(){try{return(await this._msgBroker.send({type:o.getRemoteAgentPinnedStatusRequest,data:{}})).data}catch(e){return console.error("Failed to get pinned agents from store:",e),{}}}async savePinnedAgentToStore(e,t){try{await this._msgBroker.send({type:o.setRemoteAgentPinnedStatus,data:{agentId:e,isPinned:t}})}catch(s){console.error("Failed to save pinned agent to store:",s)}}async deletePinnedAgentFromStore(e){try{await this._msgBroker.send({type:o.deleteRemoteAgentPinnedStatus,data:{agentId:e}})}catch(t){console.error("Failed to delete pinned agent from store:",t)}}async openDiffInBuffer(e,t,s){return await this._msgBroker.send({type:o.openDiffInBuffer,data:{oldContents:e,newContents:t,filePath:s}})}async getShouldShowSSHConfigPermissionPrompt(){return(await this._msgBroker.send({type:o.getShouldShowSSHConfigPermissionPromptRequest})).data}async setPermissionToWriteToSSHConfig(e){await this._msgBroker.send({type:o.setPermissionToWriteToSSHConfig,data:e})}async pauseRemoteAgentWorkspace(e){return await this._msgBroker.send({type:o.remoteAgentPauseRequest,data:{agentId:e}},3e4)}async resumeRemoteAgentWorkspace(e){return await this._msgBroker.send({type:o.remoteAgentResumeRequest,data:{agentId:e}},9e4)}async resumeHintRemoteAgent(e,t=B.viewingAgent){return await this._msgBroker.send({type:o.remoteAgentResumeHintRequest,data:{agentId:e,hintReason:t}},1e4)}async updateRemoteAgentTitle(e,t,s=1e4){return await this._msgBroker.send({type:o.updateRemoteAgentRequest,data:{agentId:e,newTitle:t}},s)}async reportRemoteAgentEvent(e){await this._msgBroker.send({type:o.reportRemoteAgentEvent,data:e})}async getRemoteAgentStatus(){return await this._msgBroker.send({type:o.getRemoteAgentStatus})}async focusAugmentPanel(){await this._msgBroker.send({type:o.showAugmentPanel})}}m(M,"key","remoteAgentsClient");export{M as R,x as S,G as a,X as b,V as c,C as d,U as e,W as f,j as g,N as h,Q as i,K as j,z as k,J as l,b as s};
