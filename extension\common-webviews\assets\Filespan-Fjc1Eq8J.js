import{S as W,i as X,s as L,W as Y,G as B,t as u,q as d,K as H,O as J,E as M,d as _,c as b,D as P,a8 as N,a as Z,o as q,p as D,X as O,a9 as E,g as aa,e as w,L as na,aa as ea,N as z,Q as k,Y as Q,h as I,R,T,U,V,a1 as S}from"./SpinnerAugment-BRONhjN7.js";import{n as ta,g as ca,a as la}from"./file-paths-CfWKsX8l.js";const oa=e=>({}),j=e=>({}),ia=e=>({}),A=e=>({});function F(e){let a;const l=e[11].leftIcon,c=R(l,e,e[12],A);return{c(){c&&c.c()},m(n,t){c&&c.m(n,t),a=!0},p(n,t){c&&c.p&&(!a||4096&t)&&T(c,l,n,n[12],a?V(l,n[12],t,ia):U(n[12]),A)},i(n){a||(d(c,n),a=!0)},o(n){u(c,n),a=!1},d(n){c&&c.d(n)}}}function G(e){let a,l,c;return{c(){a=z("div"),l=z("div"),c=Q(e[6]),I(l,"class","c-filespan__dir-text svelte-9pfhnp"),I(a,"class","c-filespan__dir svelte-9pfhnp"),S(a,"growname",e[3])},m(n,t){b(n,a,t),w(a,l),w(l,c)},p(n,t){64&t&&O(c,n[6]),8&t&&S(a,"growname",n[3])},d(n){n&&_(a)}}}function K(e){let a,l;const c=e[11].rightIcon,n=R(c,e,e[12],j);return{c(){a=z("span"),n&&n.c(),I(a,"class","right-icons svelte-9pfhnp")},m(t,s){b(t,a,s),n&&n.m(a,null),l=!0},p(t,s){n&&n.p&&(!l||4096&s)&&T(n,c,t,t[12],l?V(c,t[12],s,oa):U(t[12]),j)},i(t){l||(d(n,t),l=!0)},o(t){u(n,t),l=!1},d(t){t&&_(a),n&&n.d(t)}}}function C(e){let a,l,c,n,t,s,h,g,m,x,y,o=e[8].leftIcon&&F(e),r=!e[2]&&G(e),i=e[8].rightIcon&&K(e),v=[{class:h=N(`c-filespan ${e[0]}`)+" svelte-9pfhnp"},{role:g=e[4]?"button":""},{tabindex:"0"}],p={};for(let $=0;$<v.length;$+=1)p=Z(p,v[$]);return{c(){a=z(e[5]),o&&o.c(),l=k(),c=z("span"),n=Q(e[7]),t=k(),r&&r.c(),s=k(),i&&i.c(),I(c,"class","c-filespan__filename svelte-9pfhnp"),E(e[5])(a,p)},m($,f){b($,a,f),o&&o.m(a,null),w(a,l),w(a,c),w(c,n),w(a,t),r&&r.m(a,null),w(a,s),i&&i.m(a,null),m=!0,x||(y=na(a,"click",function(){ea(e[4])&&e[4].apply(this,arguments)}),x=!0)},p($,f){(e=$)[8].leftIcon?o?(o.p(e,f),256&f&&d(o,1)):(o=F(e),o.c(),d(o,1),o.m(a,l)):o&&(q(),u(o,1,1,()=>{o=null}),D()),(!m||128&f)&&O(n,e[7]),e[2]?r&&(r.d(1),r=null):r?r.p(e,f):(r=G(e),r.c(),r.m(a,s)),e[8].rightIcon?i?(i.p(e,f),256&f&&d(i,1)):(i=K(e),i.c(),d(i,1),i.m(a,null)):i&&(q(),u(i,1,1,()=>{i=null}),D()),E(e[5])(a,p=aa(v,[(!m||1&f&&h!==(h=N(`c-filespan ${e[0]}`)+" svelte-9pfhnp"))&&{class:h},(!m||16&f&&g!==(g=e[4]?"button":""))&&{role:g},{tabindex:"0"}]))},i($){m||(d(o),d(i),m=!0)},o($){u(o),u(i),m=!1},d($){$&&_(a),o&&o.d(),r&&r.d(),i&&i.d(),x=!1,y()}}}function pa(e){let a,l,c=e[5],n=e[5]&&C(e);return{c(){n&&n.c(),a=P()},m(t,s){n&&n.m(t,s),b(t,a,s),l=!0},p(t,s){t[5]?c?L(c,t[5])?(n.d(1),n=C(t),c=t[5],n.c(),n.m(a.parentNode,a)):n.p(t,s):(n=C(t),c=t[5],n.c(),n.m(a.parentNode,a)):c&&(n.d(1),n=null,c=t[5])},i(t){l||(d(n,t),l=!0)},o(t){u(n,t),l=!1},d(t){t&&_(a),n&&n.d(t)}}}function ra(e){let a,l;return a=new Y({props:{size:e[1],$$slots:{default:[pa]},$$scope:{ctx:e}}}),{c(){J(a.$$.fragment)},m(c,n){H(a,c,n),l=!0},p(c,[n]){const t={};2&n&&(t.size=c[1]),4605&n&&(t.$$scope={dirty:n,ctx:c}),a.$set(t)},i(c){l||(d(a.$$.fragment,c),l=!0)},o(c){u(a.$$.fragment,c),l=!1},d(c){B(a,c)}}}function sa(e,a,l){let c,n,t,s,{$$slots:h={},$$scope:g}=a;const m=M(h);let{class:x=""}=a,{filepath:y}=a,{size:o=1}=a,{nopath:r=!1}=a,{growname:i=!0}=a,{onClick:v}=a;return e.$$set=p=>{"class"in p&&l(0,x=p.class),"filepath"in p&&l(9,y=p.filepath),"size"in p&&l(1,o=p.size),"nopath"in p&&l(2,r=p.nopath),"growname"in p&&l(3,i=p.growname),"onClick"in p&&l(4,v=p.onClick),"$$scope"in p&&l(12,g=p.$$scope)},e.$$.update=()=>{512&e.$$.dirty&&l(10,c=ta(y)),1024&e.$$.dirty&&l(7,n=ca(c)),1024&e.$$.dirty&&l(6,t=la(c)),16&e.$$.dirty&&l(5,s=v?"button":"div")},[x,o,r,i,v,s,t,n,m,y,c,h,g]}class da extends W{constructor(a){super(),X(this,a,sa,ra,L,{class:0,filepath:9,size:1,nopath:2,growname:3,onClick:4})}}export{da as F};
